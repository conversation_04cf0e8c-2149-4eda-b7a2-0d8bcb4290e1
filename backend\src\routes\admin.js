const express = require('express');
const { body, query, validationResult } = require('express-validator');
const bcrypt = require('bcryptjs');
const db = require('../config/database');
const logger = require('../utils/logger');

const router = express.Router();

// Admin middleware (check if user is admin)
const adminAuth = async (req, res, next) => {
  try {
    const user = await db('users')
      .where('id', req.user.id)
      .where('role', 'admin')
      .first();

    if (!user) {
      return res.status(403).json({
        success: false,
        message: 'Access denied. Admin privileges required.',
        message_ar: 'تم رفض الوصول. مطلوب صلاحيات المدير.'
      });
    }

    next();
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      message_ar: 'خطأ في الخادم الداخلي'
    });
  }
};

// Apply admin auth to all routes
router.use(adminAuth);

// Dashboard statistics
router.get('/dashboard', async (req, res) => {
  try {
    const stats = await Promise.all([
      // Total users
      db('users').count('* as count').where('role', 'user').first(),
      // Total stores
      db('stores').count('* as count').first(),
      // Total captains
      db('captains').count('* as count').first(),
      // Total orders
      db('orders').count('* as count').first(),
      // Orders by status
      db('orders').select('status').count('* as count').groupBy('status'),
      // Revenue (delivered orders)
      db('orders').sum('total_amount as revenue').where('status', 'delivered').first(),
      // Recent orders
      db('orders')
        .select(
          'orders.*',
          'users.name as user_name',
          'stores.name as store_name'
        )
        .join('users', 'orders.user_id', 'users.id')
        .join('stores', 'orders.store_id', 'stores.id')
        .orderBy('orders.created_at', 'desc')
        .limit(10)
    ]);

    const [
      { count: totalUsers },
      { count: totalStores },
      { count: totalCaptains },
      { count: totalOrders },
      ordersByStatus,
      { revenue },
      recentOrders
    ] = stats;

    res.json({
      success: true,
      data: {
        totalUsers: parseInt(totalUsers),
        totalStores: parseInt(totalStores),
        totalCaptains: parseInt(totalCaptains),
        totalOrders: parseInt(totalOrders),
        revenue: parseFloat(revenue) || 0,
        ordersByStatus,
        recentOrders
      }
    });
  } catch (error) {
    logger.error('Error fetching dashboard stats:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      message_ar: 'خطأ في الخادم الداخلي'
    });
  }
});

// Get all users with pagination
router.get('/users', [
  query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100'),
  query('search').optional().isString().withMessage('Search must be a string'),
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation error',
        message_ar: 'خطأ في التحقق من البيانات',
        errors: errors.array()
      });
    }

    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const offset = (page - 1) * limit;
    const { search } = req.query;

    let query = db('users')
      .select('id', 'name', 'email', 'phone', 'role', 'is_active', 'created_at')
      .where('role', 'user');

    if (search) {
      query = query.where(function() {
        this.where('name', 'ilike', `%${search}%`)
            .orWhere('email', 'ilike', `%${search}%`)
            .orWhere('phone', 'ilike', `%${search}%`);
      });
    }

    const users = await query
      .orderBy('created_at', 'desc')
      .limit(limit)
      .offset(offset);

    // Get total count
    let countQuery = db('users').count('* as count').where('role', 'user');
    if (search) {
      countQuery = countQuery.where(function() {
        this.where('name', 'ilike', `%${search}%`)
            .orWhere('email', 'ilike', `%${search}%`)
            .orWhere('phone', 'ilike', `%${search}%`);
      });
    }

    const [{ count: total }] = await countQuery;

    res.json({
      success: true,
      data: {
        users,
        pagination: {
          page,
          limit,
          total: parseInt(total),
          pages: Math.ceil(total / limit)
        }
      }
    });
  } catch (error) {
    logger.error('Error fetching users:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      message_ar: 'خطأ في الخادم الداخلي'
    });
  }
});

// Toggle user active status
router.put('/users/:id/toggle-status', async (req, res) => {
  try {
    const { id } = req.params;

    const user = await db('users')
      .where('id', id)
      .where('role', 'user')
      .first();

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found',
        message_ar: 'المستخدم غير موجود'
      });
    }

    await db('users')
      .where('id', id)
      .update({
        is_active: !user.is_active,
        updated_at: new Date()
      });

    res.json({
      success: true,
      message: `User ${user.is_active ? 'deactivated' : 'activated'} successfully`,
      message_ar: `تم ${user.is_active ? 'إلغاء تفعيل' : 'تفعيل'} المستخدم بنجاح`
    });
  } catch (error) {
    logger.error('Error toggling user status:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      message_ar: 'خطأ في الخادم الداخلي'
    });
  }
});

// Get all stores
router.get('/stores', [
  query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100'),
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation error',
        message_ar: 'خطأ في التحقق من البيانات',
        errors: errors.array()
      });
    }

    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const offset = (page - 1) * limit;

    const stores = await db('stores')
      .select(
        'stores.*',
        db.raw('COALESCE(AVG(reviews.rating), 0) as average_rating'),
        db.raw('COUNT(reviews.id) as review_count')
      )
      .leftJoin('reviews', 'stores.id', 'reviews.store_id')
      .groupBy('stores.id')
      .orderBy('stores.created_at', 'desc')
      .limit(limit)
      .offset(offset);

    const [{ count: total }] = await db('stores').count('* as count');

    res.json({
      success: true,
      data: {
        stores,
        pagination: {
          page,
          limit,
          total: parseInt(total),
          pages: Math.ceil(total / limit)
        }
      }
    });
  } catch (error) {
    logger.error('Error fetching stores:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      message_ar: 'خطأ في الخادم الداخلي'
    });
  }
});

// Create new store
router.post('/stores', [
  body('name').notEmpty().withMessage('Store name is required'),
  body('email').isEmail().withMessage('Valid email is required'),
  body('phone').isMobilePhone().withMessage('Valid phone number is required'),
  body('address').notEmpty().withMessage('Address is required'),
  body('latitude').isFloat().withMessage('Valid latitude is required'),
  body('longitude').isFloat().withMessage('Valid longitude is required'),
  body('category').notEmpty().withMessage('Category is required'),
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation error',
        message_ar: 'خطأ في التحقق من البيانات',
        errors: errors.array()
      });
    }

    const {
      name, email, phone, address, latitude, longitude,
      category, description, delivery_fee, minimum_order
    } = req.body;

    // Check if store with same email or phone exists
    const existingStore = await db('stores')
      .where('email', email)
      .orWhere('phone', phone)
      .first();

    if (existingStore) {
      return res.status(400).json({
        success: false,
        message: 'Store with this email or phone already exists',
        message_ar: 'متجر بهذا البريد الإلكتروني أو رقم الهاتف موجود مسبقاً'
      });
    }

    const [storeId] = await db('stores').insert({
      name,
      email,
      phone,
      address,
      latitude,
      longitude,
      category,
      description,
      delivery_fee: delivery_fee || 0,
      minimum_order: minimum_order || 0,
      is_active: true,
      created_at: new Date(),
      updated_at: new Date()
    }).returning('id');

    const newStore = await db('stores').where('id', storeId).first();

    res.status(201).json({
      success: true,
      message: 'Store created successfully',
      message_ar: 'تم إنشاء المتجر بنجاح',
      data: newStore
    });
  } catch (error) {
    logger.error('Error creating store:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      message_ar: 'خطأ في الخادم الداخلي'
    });
  }
});

// Toggle store active status
router.put('/stores/:id/toggle-status', async (req, res) => {
  try {
    const { id } = req.params;

    const store = await db('stores').where('id', id).first();

    if (!store) {
      return res.status(404).json({
        success: false,
        message: 'Store not found',
        message_ar: 'المتجر غير موجود'
      });
    }

    await db('stores')
      .where('id', id)
      .update({
        is_active: !store.is_active,
        updated_at: new Date()
      });

    res.json({
      success: true,
      message: `Store ${store.is_active ? 'deactivated' : 'activated'} successfully`,
      message_ar: `تم ${store.is_active ? 'إلغاء تفعيل' : 'تفعيل'} المتجر بنجاح`
    });
  } catch (error) {
    logger.error('Error toggling store status:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      message_ar: 'خطأ في الخادم الداخلي'
    });
  }
});

// Get all captains
router.get('/captains', [
  query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100'),
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation error',
        message_ar: 'خطأ في التحقق من البيانات',
        errors: errors.array()
      });
    }

    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const offset = (page - 1) * limit;

    const captains = await db('captains')
      .select('id', 'name', 'phone', 'email', 'status', 'vehicle_type', 'vehicle_number', 'rating', 'total_deliveries', 'is_active', 'created_at')
      .orderBy('created_at', 'desc')
      .limit(limit)
      .offset(offset);

    const [{ count: total }] = await db('captains').count('* as count');

    res.json({
      success: true,
      data: {
        captains,
        pagination: {
          page,
          limit,
          total: parseInt(total),
          pages: Math.ceil(total / limit)
        }
      }
    });
  } catch (error) {
    logger.error('Error fetching captains:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      message_ar: 'خطأ في الخادم الداخلي'
    });
  }
});

// Create new captain
router.post('/captains', [
  body('name').notEmpty().withMessage('Captain name is required'),
  body('phone').isMobilePhone().withMessage('Valid phone number is required'),
  body('email').isEmail().withMessage('Valid email is required'),
  body('password').isLength({ min: 6 }).withMessage('Password must be at least 6 characters'),
  body('vehicle_type').notEmpty().withMessage('Vehicle type is required'),
  body('vehicle_number').notEmpty().withMessage('Vehicle number is required'),
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation error',
        message_ar: 'خطأ في التحقق من البيانات',
        errors: errors.array()
      });
    }

    const { name, phone, email, password, vehicle_type, vehicle_number } = req.body;

    // Check if captain with same email or phone exists
    const existingCaptain = await db('captains')
      .where('email', email)
      .orWhere('phone', phone)
      .first();

    if (existingCaptain) {
      return res.status(400).json({
        success: false,
        message: 'Captain with this email or phone already exists',
        message_ar: 'كابتن بهذا البريد الإلكتروني أو رقم الهاتف موجود مسبقاً'
      });
    }

    const hashedPassword = await bcrypt.hash(password, 12);

    const [captainId] = await db('captains').insert({
      name,
      phone,
      email,
      password: hashedPassword,
      vehicle_type,
      vehicle_number,
      status: 'offline',
      is_active: true,
      created_at: new Date(),
      updated_at: new Date()
    }).returning('id');

    const newCaptain = await db('captains')
      .select('id', 'name', 'phone', 'email', 'vehicle_type', 'vehicle_number', 'status', 'is_active', 'created_at')
      .where('id', captainId)
      .first();

    res.status(201).json({
      success: true,
      message: 'Captain created successfully',
      message_ar: 'تم إنشاء الكابتن بنجاح',
      data: newCaptain
    });
  } catch (error) {
    logger.error('Error creating captain:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      message_ar: 'خطأ في الخادم الداخلي'
    });
  }
});

module.exports = router;
