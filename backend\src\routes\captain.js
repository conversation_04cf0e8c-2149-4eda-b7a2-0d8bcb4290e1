const express = require('express');
const { body, query, validationResult } = require('express-validator');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const db = require('../config/database');
const logger = require('../utils/logger');

const router = express.Router();

// Captain authentication middleware
const captainAuth = async (req, res, next) => {
  try {
    const token = req.header('Authorization')?.replace('Bearer ', '');
    
    if (!token) {
      return res.status(401).json({
        success: false,
        message: 'Access denied. No token provided.',
        message_ar: 'تم رفض الوصول. لم يتم توفير رمز المصادقة.'
      });
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    const captain = await db('captains').where('id', decoded.id).first();

    if (!captain) {
      return res.status(401).json({
        success: false,
        message: 'Invalid token.',
        message_ar: 'رمز مصادقة غير صالح.'
      });
    }

    req.captain = captain;
    next();
  } catch (error) {
    res.status(401).json({
      success: false,
      message: 'Invalid token.',
      message_ar: 'رمز مصادقة غير صالح.'
    });
  }
};

// Captain login
router.post('/login', [
  body('phone').isMobilePhone().withMessage('Valid phone number is required'),
  body('password').isLength({ min: 6 }).withMessage('Password must be at least 6 characters'),
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation error',
        message_ar: 'خطأ في التحقق من البيانات',
        errors: errors.array()
      });
    }

    const { phone, password } = req.body;

    const captain = await db('captains')
      .where('phone', phone)
      .where('is_active', true)
      .first();

    if (!captain) {
      return res.status(401).json({
        success: false,
        message: 'Invalid credentials',
        message_ar: 'بيانات الدخول غير صحيحة'
      });
    }

    const isPasswordValid = await bcrypt.compare(password, captain.password);
    if (!isPasswordValid) {
      return res.status(401).json({
        success: false,
        message: 'Invalid credentials',
        message_ar: 'بيانات الدخول غير صحيحة'
      });
    }

    const token = jwt.sign(
      { id: captain.id, phone: captain.phone },
      process.env.JWT_SECRET,
      { expiresIn: '30d' }
    );

    // Update last login
    await db('captains')
      .where('id', captain.id)
      .update({ last_login: new Date() });

    res.json({
      success: true,
      message: 'Login successful',
      message_ar: 'تم تسجيل الدخول بنجاح',
      data: {
        token,
        captain: {
          id: captain.id,
          name: captain.name,
          phone: captain.phone,
          email: captain.email,
          status: captain.status,
          vehicle_type: captain.vehicle_type,
          vehicle_number: captain.vehicle_number
        }
      }
    });
  } catch (error) {
    logger.error('Captain login error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      message_ar: 'خطأ في الخادم الداخلي'
    });
  }
});

// Get captain profile
router.get('/profile', captainAuth, async (req, res) => {
  try {
    const captain = await db('captains')
      .select('id', 'name', 'phone', 'email', 'status', 'vehicle_type', 'vehicle_number', 'rating', 'total_deliveries', 'created_at')
      .where('id', req.captain.id)
      .first();

    res.json({
      success: true,
      data: captain
    });
  } catch (error) {
    logger.error('Error fetching captain profile:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      message_ar: 'خطأ في الخادم الداخلي'
    });
  }
});

// Update captain status
router.put('/status', captainAuth, [
  body('status').isIn(['available', 'busy', 'offline']).withMessage('Invalid status'),
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation error',
        message_ar: 'خطأ في التحقق من البيانات',
        errors: errors.array()
      });
    }

    const { status } = req.body;

    await db('captains')
      .where('id', req.captain.id)
      .update({
        status,
        updated_at: new Date()
      });

    res.json({
      success: true,
      message: 'Status updated successfully',
      message_ar: 'تم تحديث الحالة بنجاح'
    });
  } catch (error) {
    logger.error('Error updating captain status:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      message_ar: 'خطأ في الخادم الداخلي'
    });
  }
});

// Update captain location
router.put('/location', captainAuth, [
  body('latitude').isFloat().withMessage('Valid latitude is required'),
  body('longitude').isFloat().withMessage('Valid longitude is required'),
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation error',
        message_ar: 'خطأ في التحقق من البيانات',
        errors: errors.array()
      });
    }

    const { latitude, longitude } = req.body;

    await db('captains')
      .where('id', req.captain.id)
      .update({
        latitude,
        longitude,
        last_location_update: new Date(),
        updated_at: new Date()
      });

    // Emit location update to tracking users
    const io = req.app.get('io');
    io.to(`tracking_${req.captain.id}`).emit('captain_location', {
      captainId: req.captain.id,
      latitude,
      longitude,
      timestamp: new Date().toISOString()
    });

    res.json({
      success: true,
      message: 'Location updated successfully',
      message_ar: 'تم تحديث الموقع بنجاح'
    });
  } catch (error) {
    logger.error('Error updating captain location:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      message_ar: 'خطأ في الخادم الداخلي'
    });
  }
});

// Get assigned orders
router.get('/orders', captainAuth, [
  query('status').optional().isIn(['assigned', 'picked_up', 'delivered']).withMessage('Invalid status'),
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation error',
        message_ar: 'خطأ في التحقق من البيانات',
        errors: errors.array()
      });
    }

    const { status } = req.query;

    let query = db('orders')
      .select(
        'orders.*',
        'stores.name as store_name',
        'stores.address as store_address',
        'stores.latitude as store_latitude',
        'stores.longitude as store_longitude',
        'addresses.address as delivery_address',
        'addresses.latitude as delivery_latitude',
        'addresses.longitude as delivery_longitude',
        'users.name as customer_name',
        'users.phone as customer_phone'
      )
      .join('stores', 'orders.store_id', 'stores.id')
      .join('addresses', 'orders.address_id', 'addresses.id')
      .join('users', 'orders.user_id', 'users.id')
      .where('orders.captain_id', req.captain.id);

    if (status) {
      query = query.where('orders.status', status);
    } else {
      query = query.whereIn('orders.status', ['assigned', 'picked_up']);
    }

    const orders = await query.orderBy('orders.created_at', 'desc');

    res.json({
      success: true,
      data: orders
    });
  } catch (error) {
    logger.error('Error fetching captain orders:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      message_ar: 'خطأ في الخادم الداخلي'
    });
  }
});

// Accept order assignment
router.put('/orders/:id/accept', captainAuth, async (req, res) => {
  try {
    const { id } = req.params;

    const order = await db('orders')
      .where('id', id)
      .where('captain_id', req.captain.id)
      .where('status', 'assigned')
      .first();

    if (!order) {
      return res.status(404).json({
        success: false,
        message: 'Order not found or already processed',
        message_ar: 'الطلب غير موجود أو تم معالجته مسبقاً'
      });
    }

    await db('orders')
      .where('id', id)
      .update({
        status: 'on_way',
        updated_at: new Date()
      });

    // Update captain status
    await db('captains')
      .where('id', req.captain.id)
      .update({ status: 'busy' });

    // Emit socket events
    const io = req.app.get('io');
    io.to(`user_${order.user_id}`).emit('order_status_update', {
      order_id: id,
      status: 'on_way',
      message: 'Captain is on the way',
      message_ar: 'الكابتن في الطريق'
    });

    res.json({
      success: true,
      message: 'Order accepted successfully',
      message_ar: 'تم قبول الطلب بنجاح'
    });
  } catch (error) {
    logger.error('Error accepting order:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      message_ar: 'خطأ في الخادم الداخلي'
    });
  }
});

// Mark order as picked up
router.put('/orders/:id/pickup', captainAuth, async (req, res) => {
  try {
    const { id } = req.params;

    const order = await db('orders')
      .where('id', id)
      .where('captain_id', req.captain.id)
      .where('status', 'on_way')
      .first();

    if (!order) {
      return res.status(404).json({
        success: false,
        message: 'Order not found or invalid status',
        message_ar: 'الطلب غير موجود أو الحالة غير صالحة'
      });
    }

    await db('orders')
      .where('id', id)
      .update({
        status: 'picked_up',
        picked_up_at: new Date(),
        updated_at: new Date()
      });

    // Emit socket events
    const io = req.app.get('io');
    io.to(`user_${order.user_id}`).emit('order_status_update', {
      order_id: id,
      status: 'picked_up',
      message: 'Order picked up from store',
      message_ar: 'تم استلام الطلب من المتجر'
    });

    res.json({
      success: true,
      message: 'Order marked as picked up',
      message_ar: 'تم تحديد الطلب كمستلم'
    });
  } catch (error) {
    logger.error('Error marking order as picked up:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      message_ar: 'خطأ في الخادم الداخلي'
    });
  }
});

// Mark order as delivered
router.put('/orders/:id/deliver', captainAuth, async (req, res) => {
  try {
    const { id } = req.params;

    const order = await db('orders')
      .where('id', id)
      .where('captain_id', req.captain.id)
      .where('status', 'picked_up')
      .first();

    if (!order) {
      return res.status(404).json({
        success: false,
        message: 'Order not found or invalid status',
        message_ar: 'الطلب غير موجود أو الحالة غير صالحة'
      });
    }

    await db('orders')
      .where('id', id)
      .update({
        status: 'delivered',
        delivered_at: new Date(),
        updated_at: new Date()
      });

    // Update captain stats
    await db('captains')
      .where('id', req.captain.id)
      .increment('total_deliveries', 1)
      .update({ status: 'available' });

    // Emit socket events
    const io = req.app.get('io');
    io.to(`user_${order.user_id}`).emit('order_status_update', {
      order_id: id,
      status: 'delivered',
      message: 'Order delivered successfully',
      message_ar: 'تم تسليم الطلب بنجاح'
    });

    res.json({
      success: true,
      message: 'Order marked as delivered',
      message_ar: 'تم تحديد الطلب كمسلم'
    });
  } catch (error) {
    logger.error('Error marking order as delivered:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      message_ar: 'خطأ في الخادم الداخلي'
    });
  }
});

module.exports = router;
