/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function(knex) {
  return knex.schema.createTable('reviews', function(table) {
    table.string('id', 36).primary().defaultTo(knex.raw("(lower(hex(randomblob(4))) || '-' || lower(hex(randomblob(2))) || '-4' || substr(lower(hex(randomblob(2))),2) || '-' || substr('89ab',abs(random()) % 4 + 1, 1) || substr(lower(hex(randomblob(2))),2) || '-' || lower(hex(randomblob(6))))"));
    table.string('user_id', 36).notNullable().references('id').inTable('users').onDelete('CASCADE');
    table.string('order_id', 36).notNullable().references('id').inTable('orders').onDelete('CASCADE');
    table.string('store_id', 36).nullable().references('id').inTable('stores').onDelete('CASCADE');
    table.string('captain_id', 36).nullable().references('id').inTable('captains').onDelete('CASCADE');
    table.string('product_id', 36).nullable().references('id').inTable('products').onDelete('CASCADE');
    table.enum('review_type', ['store', 'captain', 'product']).notNullable();
    table.integer('rating').notNullable().checkBetween([1, 5]);
    table.text('comment').nullable();
    table.json('images').nullable(); // Array of image URLs
    table.boolean('is_anonymous').defaultTo(false);
    table.timestamps(true, true);
    
    // Indexes
    table.index(['user_id']);
    table.index(['order_id']);
    table.index(['store_id']);
    table.index(['captain_id']);
    table.index(['product_id']);
    table.index(['review_type']);
    table.index(['rating']);
    table.index(['created_at']);
    
    // Unique constraint to prevent duplicate reviews
    table.unique(['user_id', 'order_id', 'review_type', 'store_id', 'captain_id', 'product_id']);
  });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function(knex) {
  return knex.schema.dropTable('reviews');
};
