import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:hive_flutter/hive_flutter.dart';

class StorageService {
  static late SharedPreferences _prefs;
  static const FlutterSecureStorage _secureStorage = FlutterSecureStorage(
    aOptions: AndroidOptions(),
    iOptions: IOSOptions(
      accessibility: KeychainAccessibility.first_unlock_this_device,
    ),
  );
  static late Box _hiveBox;

  // Initialize storage services
  static Future<void> init() async {
    _prefs = await SharedPreferences.getInstance();
    _hiveBox = await Hive.openBox('trago_cache');
  }

  // SharedPreferences methods
  static Future<bool> setString(String key, String value) async {
    return await _prefs.setString(key, value);
  }

  static String? getString(String key) {
    return _prefs.getString(key);
  }

  static Future<bool> setBool(String key, bool value) async {
    return await _prefs.setBool(key, value);
  }

  static bool? getBool(String key) {
    return _prefs.getBool(key);
  }

  static Future<bool> setInt(String key, int value) async {
    return await _prefs.setInt(key, value);
  }

  static int? getInt(String key) {
    return _prefs.getInt(key);
  }

  static Future<bool> setDouble(String key, double value) async {
    return await _prefs.setDouble(key, value);
  }

  static double? getDouble(String key) {
    return _prefs.getDouble(key);
  }

  static Future<bool> setStringList(String key, List<String> value) async {
    return await _prefs.setStringList(key, value);
  }

  static List<String>? getStringList(String key) {
    return _prefs.getStringList(key);
  }

  static Future<bool> remove(String key) async {
    return await _prefs.remove(key);
  }

  static Future<bool> clear() async {
    return await _prefs.clear();
  }

  static bool containsKey(String key) {
    return _prefs.containsKey(key);
  }

  // JSON storage methods
  static Future<bool> setJson(String key, Map<String, dynamic> value) async {
    final jsonString = jsonEncode(value);
    return await setString(key, jsonString);
  }

  static Map<String, dynamic>? getJson(String key) {
    final jsonString = getString(key);
    if (jsonString != null) {
      try {
        return jsonDecode(jsonString) as Map<String, dynamic>;
      } catch (e) {
        return null;
      }
    }
    return null;
  }

  // Secure storage methods (for sensitive data like tokens)
  static Future<void> setSecureString(String key, String value) async {
    await _secureStorage.write(key: key, value: value);
  }

  static Future<String?> getSecureString(String key) async {
    return await _secureStorage.read(key: key);
  }

  static Future<void> setSecureJson(
      String key, Map<String, dynamic> value) async {
    final jsonString = jsonEncode(value);
    await _secureStorage.write(key: key, value: jsonString);
  }

  static Future<Map<String, dynamic>?> getSecureJson(String key) async {
    final jsonString = await _secureStorage.read(key: key);
    if (jsonString != null) {
      try {
        return jsonDecode(jsonString) as Map<String, dynamic>;
      } catch (e) {
        return null;
      }
    }
    return null;
  }

  static Future<void> removeSecure(String key) async {
    await _secureStorage.delete(key: key);
  }

  static Future<void> clearSecure() async {
    await _secureStorage.deleteAll();
  }

  static Future<bool> containsSecureKey(String key) async {
    return await _secureStorage.containsKey(key: key);
  }

  // Hive cache methods (for temporary data with expiration)
  static Future<void> setCacheString(String key, String value,
      {Duration? expiry}) async {
    final data = {
      'value': value,
      'expiry': expiry != null
          ? DateTime.now().add(expiry).millisecondsSinceEpoch
          : null,
    };
    await _hiveBox.put(key, data);
  }

  static String? getCacheString(String key) {
    final data = _hiveBox.get(key);
    if (data != null && data is Map) {
      final expiry = data['expiry'];
      if (expiry != null && DateTime.now().millisecondsSinceEpoch > expiry) {
        _hiveBox.delete(key);
        return null;
      }
      return data['value'] as String?;
    }
    return null;
  }

  static Future<void> setCacheJson(String key, Map<String, dynamic> value,
      {Duration? expiry}) async {
    final data = {
      'value': value,
      'expiry': expiry != null
          ? DateTime.now().add(expiry).millisecondsSinceEpoch
          : null,
    };
    await _hiveBox.put(key, data);
  }

  static Map<String, dynamic>? getCacheJson(String key) {
    final data = _hiveBox.get(key);
    if (data != null && data is Map) {
      final expiry = data['expiry'];
      if (expiry != null && DateTime.now().millisecondsSinceEpoch > expiry) {
        _hiveBox.delete(key);
        return null;
      }
      return Map<String, dynamic>.from(data['value'] as Map);
    }
    return null;
  }

  static Future<void> removeCacheKey(String key) async {
    await _hiveBox.delete(key);
  }

  static Future<void> clearCache() async {
    await _hiveBox.clear();
  }

  static bool containsCacheKey(String key) {
    return _hiveBox.containsKey(key);
  }

  // Utility methods
  static Future<void> clearExpiredCache() async {
    final keys = _hiveBox.keys.toList();
    for (final key in keys) {
      final data = _hiveBox.get(key);
      if (data != null && data is Map) {
        final expiry = data['expiry'];
        if (expiry != null && DateTime.now().millisecondsSinceEpoch > expiry) {
          await _hiveBox.delete(key);
        }
      }
    }
  }

  static Future<void> clearAllData() async {
    await clear();
    await clearSecure();
    await clearCache();
  }

  // User-specific storage methods
  static Future<void> setUserToken(String token) async {
    await setSecureString('user_token', token);
  }

  static Future<String?> getUserToken() async {
    return await getSecureString('user_token');
  }

  static Future<void> setRefreshToken(String token) async {
    await setSecureString('refresh_token', token);
  }

  static Future<String?> getRefreshToken() async {
    return await getSecureString('refresh_token');
  }

  static Future<void> setUserData(Map<String, dynamic> userData) async {
    await setSecureJson('user_data', userData);
  }

  static Future<Map<String, dynamic>?> getUserData() async {
    return await getSecureJson('user_data');
  }

  static Future<void> clearUserData() async {
    await removeSecure('user_token');
    await removeSecure('refresh_token');
    await removeSecure('user_data');
  }

  static Future<bool> isUserLoggedIn() async {
    final token = await getUserToken();
    return token != null && token.isNotEmpty;
  }
}
