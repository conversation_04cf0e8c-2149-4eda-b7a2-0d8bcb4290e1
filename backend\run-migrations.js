const knex = require('knex');
const config = require('./knexfile.js');

async function runMigrations() {
  const db = knex(config.development);
  
  try {
    console.log('Running migrations...');
    await db.migrate.latest();
    console.log('Migrations completed successfully!');
    
    // Check if we have any tables
    const tables = await db.raw("SELECT name FROM sqlite_master WHERE type='table'");
    console.log('Created tables:', tables.map(t => t.name));
    
  } catch (error) {
    console.error('Migration failed:', error);
  } finally {
    await db.destroy();
  }
}

runMigrations();
