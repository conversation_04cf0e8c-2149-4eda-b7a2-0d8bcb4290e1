import React, { useState } from 'react';
import { Form, Input, Button, Card, Typography, message } from 'antd';
import { UserOutlined, LockOutlined } from '@ant-design/icons';
import { useAuth } from '../contexts/AuthContext';

const { Title, Text } = Typography;

const Login = () => {
  const [loading, setLoading] = useState(false);
  const { login } = useAuth();

  const onFinish = async (values) => {
    setLoading(true);
    try {
      const result = await login(values.email, values.password);
      if (result.success) {
        message.success('تم تسجيل الدخول بنجاح');
      } else {
        message.error(result.message);
      }
    } catch (error) {
      message.error('حدث خطأ أثناء تسجيل الدخول');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="login-container">
      <Card className="login-form">
        <div style={{ textAlign: 'center', marginBottom: 32 }}>
          <Title level={2} style={{ color: '#1890ff', marginBottom: 8 }}>
            تراجو
          </Title>
          <Text type="secondary">لوحة تحكم المدير</Text>
        </div>

        <Form
          name="login"
          onFinish={onFinish}
          autoComplete="off"
          size="large"
        >
          <Form.Item
            name="email"
            rules={[
              {
                required: true,
                message: 'يرجى إدخال البريد الإلكتروني',
              },
              {
                type: 'email',
                message: 'يرجى إدخال بريد إلكتروني صحيح',
              },
            ]}
          >
            <Input
              prefix={<UserOutlined />}
              placeholder="البريد الإلكتروني"
            />
          </Form.Item>

          <Form.Item
            name="password"
            rules={[
              {
                required: true,
                message: 'يرجى إدخال كلمة المرور',
              },
            ]}
          >
            <Input.Password
              prefix={<LockOutlined />}
              placeholder="كلمة المرور"
            />
          </Form.Item>

          <Form.Item>
            <Button
              type="primary"
              htmlType="submit"
              loading={loading}
              block
              style={{ height: 48 }}
            >
              تسجيل الدخول
            </Button>
          </Form.Item>
        </Form>

        <div style={{ textAlign: 'center', marginTop: 24 }}>
          <Text type="secondary" style={{ fontSize: 12 }}>
            للاختبار: <EMAIL> / password123
          </Text>
        </div>
      </Card>
    </div>
  );
};

export default Login;
