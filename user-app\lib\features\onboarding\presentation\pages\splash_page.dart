import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../../../../core/config/theme_config.dart';
import '../../../../core/utils/app_constants.dart';

class SplashPage extends StatefulWidget {
  const SplashPage({super.key});

  @override
  State<SplashPage> createState() => _SplashPageState();
}

class _SplashPageState extends State<SplashPage> with TickerProviderStateMixin {
  late AnimationController _logoController;
  late AnimationController _textController;
  late Animation<double> _logoAnimation;
  late Animation<double> _textAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _initAnimations();
    _startAnimations();
  }

  void _initAnimations() {
    // Logo animation controller
    _logoController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    // Text animation controller
    _textController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    // Logo scale animation
    _logoAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _logoController,
      curve: Curves.elasticOut,
    ));

    // Text fade animation
    _textAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _textController,
      curve: Curves.easeInOut,
    ));

    // Text slide animation
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.5),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _textController,
      curve: Curves.easeOutCubic,
    ));
  }

  void _startAnimations() async {
    // Start logo animation
    await _logoController.forward();

    // Start text animation after logo
    await _textController.forward();

    // Wait a bit more then navigate
    await Future.delayed(const Duration(milliseconds: 500));

    // The navigation will be handled by the router redirect logic
  }

  @override
  void dispose() {
    _logoController.dispose();
    _textController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.primary,
      body: AnnotatedRegion<SystemUiOverlayStyle>(
        value: const SystemUiOverlayStyle(
          statusBarColor: Colors.transparent,
          statusBarIconBrightness: Brightness.dark,
          systemNavigationBarColor: AppColors.primary,
          systemNavigationBarIconBrightness: Brightness.dark,
        ),
        child: SafeArea(
          child: Column(
            children: [
              // Top spacer
              const Spacer(flex: 2),

              // Logo section
              Expanded(
                flex: 3,
                child: Center(
                  child: AnimatedBuilder(
                    animation: _logoAnimation,
                    builder: (context, child) {
                      return Transform.scale(
                        scale: _logoAnimation.value,
                        child: Container(
                          width: 120,
                          height: 120,
                          decoration: BoxDecoration(
                            color: AppColors.white,
                            borderRadius:
                                BorderRadius.circular(AppSizes.radiusXl),
                            boxShadow: [
                              BoxShadow(
                                color: AppColors.black.withValues(alpha: 0.1),
                                blurRadius: 20,
                                offset: const Offset(0, 10),
                              ),
                            ],
                          ),
                          child: const Icon(
                            Icons.delivery_dining,
                            size: 60,
                            color: AppColors.primary,
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ),

              // App name and tagline
              Expanded(
                flex: 2,
                child: SlideTransition(
                  position: _slideAnimation,
                  child: FadeTransition(
                    opacity: _textAnimation,
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        // App name
                        Text(
                          AppConstants.appName,
                          style: Theme.of(context)
                              .textTheme
                              .displayLarge
                              ?.copyWith(
                                color: AppColors.black,
                                fontWeight: FontWeight.bold,
                                letterSpacing: 2,
                              ),
                        ),

                        const SizedBox(height: AppSizes.sm),

                        // App name in Arabic
                        Text(
                          AppConstants.appNameArabic,
                          style: Theme.of(context)
                              .textTheme
                              .headlineMedium
                              ?.copyWith(
                                color: AppColors.black.withValues(alpha: 0.8),
                                fontWeight: FontWeight.w600,
                              ),
                        ),

                        const SizedBox(height: AppSizes.md),

                        // Tagline
                        Text(
                          AppConstants.appDescription,
                          style: Theme.of(context)
                              .textTheme
                              .bodyLarge
                              ?.copyWith(
                                color: AppColors.black.withValues(alpha: 0.7),
                                fontWeight: FontWeight.w500,
                              ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  ),
                ),
              ),

              // Loading indicator
              Expanded(
                flex: 1,
                child: FadeTransition(
                  opacity: _textAnimation,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      // Custom loading animation
                      SizedBox(
                        width: 40,
                        height: 40,
                        child: CircularProgressIndicator(
                          strokeWidth: 3,
                          valueColor: AlwaysStoppedAnimation<Color>(
                            AppColors.black.withValues(alpha: 0.7),
                          ),
                        ),
                      ),

                      const SizedBox(height: AppSizes.md),

                      // Loading text
                      Text(
                        'جاري التحميل...',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: AppColors.black.withValues(alpha: 0.6),
                            ),
                      ),
                    ],
                  ),
                ),
              ),

              // Bottom spacer
              const Spacer(flex: 1),
            ],
          ),
        ),
      ),
    );
  }
}

// Alternative splash page with Lottie animation
class LottieSplashPage extends StatefulWidget {
  const LottieSplashPage({super.key});

  @override
  State<LottieSplashPage> createState() => _LottieSplashPageState();
}

class _LottieSplashPageState extends State<LottieSplashPage>
    with TickerProviderStateMixin {
  late AnimationController _controller;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(vsync: this);
    _startAnimation();
  }

  void _startAnimation() async {
    // Wait for animation to complete
    await Future.delayed(const Duration(seconds: 3));

    // The navigation will be handled by the router redirect logic
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.primary,
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Lottie animation (if available)
            // Lottie.asset(
            //   'assets/animations/delivery_animation.json',
            //   width: 200,
            //   height: 200,
            //   controller: _controller,
            //   onLoaded: (composition) {
            //     _controller.duration = composition.duration;
            //     _controller.forward();
            //   },
            // ),

            // Fallback to simple animation
            Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                color: AppColors.white,
                borderRadius: BorderRadius.circular(AppSizes.radiusXl),
                boxShadow: [
                  BoxShadow(
                    color: AppColors.black.withValues(alpha: 0.1),
                    blurRadius: 20,
                    offset: const Offset(0, 10),
                  ),
                ],
              ),
              child: const Icon(
                Icons.delivery_dining,
                size: 60,
                color: AppColors.primary,
              ),
            ),

            const SizedBox(height: AppSizes.xl),

            // App name
            Text(
              AppConstants.appName,
              style: Theme.of(context).textTheme.displayLarge?.copyWith(
                    color: AppColors.black,
                    fontWeight: FontWeight.bold,
                  ),
            ),

            const SizedBox(height: AppSizes.sm),

            // App name in Arabic
            Text(
              AppConstants.appNameArabic,
              style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                    color: AppColors.black.withValues(alpha: 0.8),
                    fontWeight: FontWeight.w600,
                  ),
            ),
          ],
        ),
      ),
    );
  }
}
