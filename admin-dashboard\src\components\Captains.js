import React, { useState, useEffect } from 'react';
import { Table, Card, Tag, Rate, message } from 'antd';
import { CarOutlined } from '@ant-design/icons';
import axios from 'axios';

const Captains = () => {
  const [captains, setCaptains] = useState([]);
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0,
  });

  useEffect(() => {
    fetchCaptains();
  }, [pagination.current, pagination.pageSize]);

  const fetchCaptains = async () => {
    setLoading(true);
    try {
      const response = await axios.get('/api/admin/captains', {
        params: {
          page: pagination.current,
          limit: pagination.pageSize,
        },
      });

      if (response.data.success) {
        setCaptains(response.data.data.captains);
        setPagination(prev => ({
          ...prev,
          total: response.data.data.pagination.total,
        }));
      }
    } catch (error) {
      message.error('فشل في تحميل الكباتن');
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status) => {
    const colors = {
      available: 'green',
      busy: 'orange',
      offline: 'red',
    };
    return colors[status] || 'default';
  };

  const getStatusText = (status) => {
    const texts = {
      available: 'متاح',
      busy: 'مشغول',
      offline: 'غير متصل',
    };
    return texts[status] || status;
  };

  const columns = [
    {
      title: 'اسم الكابتن',
      dataIndex: 'name',
      key: 'name',
      render: (name) => (
        <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
          <CarOutlined />
          {name}
        </div>
      ),
    },
    {
      title: 'رقم الهاتف',
      dataIndex: 'phone',
      key: 'phone',
    },
    {
      title: 'البريد الإلكتروني',
      dataIndex: 'email',
      key: 'email',
    },
    {
      title: 'نوع المركبة',
      dataIndex: 'vehicle_type',
      key: 'vehicle_type',
      render: (type) => <Tag color="blue">{type}</Tag>,
    },
    {
      title: 'رقم المركبة',
      dataIndex: 'vehicle_number',
      key: 'vehicle_number',
    },
    {
      title: 'الحالة',
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <Tag color={getStatusColor(status)}>
          {getStatusText(status)}
        </Tag>
      ),
    },
    {
      title: 'التقييم',
      dataIndex: 'rating',
      key: 'rating',
      render: (rating) => (
        <Rate disabled defaultValue={parseFloat(rating) || 0} allowHalf />
      ),
    },
    {
      title: 'إجمالي التوصيلات',
      dataIndex: 'total_deliveries',
      key: 'total_deliveries',
      render: (count) => <Tag color="purple">{count}</Tag>,
    },
    {
      title: 'حالة الحساب',
      dataIndex: 'is_active',
      key: 'is_active',
      render: (isActive) => (
        <Tag color={isActive ? 'green' : 'red'}>
          {isActive ? 'نشط' : 'غير نشط'}
        </Tag>
      ),
    },
    {
      title: 'تاريخ التسجيل',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (date) => new Date(date).toLocaleDateString('ar-SA'),
    },
  ];

  return (
    <Card title="إدارة الكباتن">
      <Table
        columns={columns}
        dataSource={captains}
        rowKey="id"
        loading={loading}
        pagination={{
          ...pagination,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) =>
            `${range[0]}-${range[1]} من ${total} كابتن`,
          onChange: (page, pageSize) => {
            setPagination(prev => ({
              ...prev,
              current: page,
              pageSize,
            }));
          },
        }}
        scroll={{ x: 1200 }}
      />
    </Card>
  );
};

export default Captains;
