/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function(knex) {
  return knex.schema.createTable('order_items', function(table) {
    table.string('id', 36).primary().defaultTo(knex.raw("(lower(hex(randomblob(4))) || '-' || lower(hex(randomblob(2))) || '-4' || substr(lower(hex(randomblob(2))),2) || '-' || substr('89ab',abs(random()) % 4 + 1, 1) || substr(lower(hex(randomblob(2))),2) || '-' || lower(hex(randomblob(6))))"));
    table.string('order_id', 36).notNullable().references('id').inTable('orders').onDelete('CASCADE');
    table.string('product_id', 36).notNullable().references('id').inTable('products').onDelete('CASCADE');
    table.string('product_name', 100).notNullable(); // Store name at time of order
    table.decimal('product_price', 8, 2).notNullable(); // Store price at time of order
    table.integer('quantity').notNullable().defaultTo(1);
    table.json('selected_options').nullable(); // Store selected options like size, color
    table.text('special_instructions').nullable();
    table.decimal('item_total', 8, 2).notNullable(); // quantity * price
    table.timestamps(true, true);
    
    // Indexes
    table.index(['order_id']);
    table.index(['product_id']);
  });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function(knex) {
  return knex.schema.dropTable('order_items');
};
