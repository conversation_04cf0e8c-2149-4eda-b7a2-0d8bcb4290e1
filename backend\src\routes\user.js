const express = require('express');
const { body, validationResult } = require('express-validator');
const bcrypt = require('bcryptjs');
const db = require('../config/database');
const logger = require('../utils/logger');

const router = express.Router();

// Get user profile
router.get('/profile', async (req, res) => {
  try {
    const user = await db('users')
      .select('id', 'name', 'email', 'phone', 'created_at', 'updated_at')
      .where('id', req.user.id)
      .first();

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found',
        message_ar: 'المستخدم غير موجود'
      });
    }

    res.json({
      success: true,
      data: user
    });
  } catch (error) {
    logger.error('Error fetching user profile:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      message_ar: 'خطأ في الخادم الداخلي'
    });
  }
});

// Update user profile
router.put('/profile', [
  body('name').optional().isLength({ min: 2 }).withMessage('Name must be at least 2 characters'),
  body('phone').optional().isMobilePhone().withMessage('Invalid phone number'),
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation error',
        message_ar: 'خطأ في التحقق من البيانات',
        errors: errors.array()
      });
    }

    const { name, phone } = req.body;
    const updateData = {};

    if (name) updateData.name = name;
    if (phone) updateData.phone = phone;
    updateData.updated_at = new Date();

    await db('users')
      .where('id', req.user.id)
      .update(updateData);

    const updatedUser = await db('users')
      .select('id', 'name', 'email', 'phone', 'created_at', 'updated_at')
      .where('id', req.user.id)
      .first();

    res.json({
      success: true,
      message: 'Profile updated successfully',
      message_ar: 'تم تحديث الملف الشخصي بنجاح',
      data: updatedUser
    });
  } catch (error) {
    logger.error('Error updating user profile:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      message_ar: 'خطأ في الخادم الداخلي'
    });
  }
});

// Change password
router.put('/change-password', [
  body('currentPassword').notEmpty().withMessage('Current password is required'),
  body('newPassword').isLength({ min: 6 }).withMessage('New password must be at least 6 characters'),
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation error',
        message_ar: 'خطأ في التحقق من البيانات',
        errors: errors.array()
      });
    }

    const { currentPassword, newPassword } = req.body;

    const user = await db('users')
      .select('password')
      .where('id', req.user.id)
      .first();

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found',
        message_ar: 'المستخدم غير موجود'
      });
    }

    const isCurrentPasswordValid = await bcrypt.compare(currentPassword, user.password);
    if (!isCurrentPasswordValid) {
      return res.status(400).json({
        success: false,
        message: 'Current password is incorrect',
        message_ar: 'كلمة المرور الحالية غير صحيحة'
      });
    }

    const hashedNewPassword = await bcrypt.hash(newPassword, 12);

    await db('users')
      .where('id', req.user.id)
      .update({
        password: hashedNewPassword,
        updated_at: new Date()
      });

    res.json({
      success: true,
      message: 'Password changed successfully',
      message_ar: 'تم تغيير كلمة المرور بنجاح'
    });
  } catch (error) {
    logger.error('Error changing password:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      message_ar: 'خطأ في الخادم الداخلي'
    });
  }
});

// Get user addresses
router.get('/addresses', async (req, res) => {
  try {
    const addresses = await db('addresses')
      .where('user_id', req.user.id)
      .orderBy('is_default', 'desc')
      .orderBy('created_at', 'desc');

    res.json({
      success: true,
      data: addresses
    });
  } catch (error) {
    logger.error('Error fetching user addresses:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      message_ar: 'خطأ في الخادم الداخلي'
    });
  }
});

// Add new address
router.post('/addresses', [
  body('title').notEmpty().withMessage('Address title is required'),
  body('address').notEmpty().withMessage('Address is required'),
  body('latitude').isFloat().withMessage('Valid latitude is required'),
  body('longitude').isFloat().withMessage('Valid longitude is required'),
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation error',
        message_ar: 'خطأ في التحقق من البيانات',
        errors: errors.array()
      });
    }

    const { title, address, latitude, longitude, is_default } = req.body;

    // If this is set as default, unset other defaults
    if (is_default) {
      await db('addresses')
        .where('user_id', req.user.id)
        .update({ is_default: false });
    }

    const [addressId] = await db('addresses').insert({
      user_id: req.user.id,
      title,
      address,
      latitude,
      longitude,
      is_default: is_default || false,
      created_at: new Date(),
      updated_at: new Date()
    }).returning('id');

    const newAddress = await db('addresses')
      .where('id', addressId)
      .first();

    res.status(201).json({
      success: true,
      message: 'Address added successfully',
      message_ar: 'تم إضافة العنوان بنجاح',
      data: newAddress
    });
  } catch (error) {
    logger.error('Error adding address:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      message_ar: 'خطأ في الخادم الداخلي'
    });
  }
});

// Update address
router.put('/addresses/:id', [
  body('title').optional().notEmpty().withMessage('Address title cannot be empty'),
  body('address').optional().notEmpty().withMessage('Address cannot be empty'),
  body('latitude').optional().isFloat().withMessage('Valid latitude is required'),
  body('longitude').optional().isFloat().withMessage('Valid longitude is required'),
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation error',
        message_ar: 'خطأ في التحقق من البيانات',
        errors: errors.array()
      });
    }

    const { id } = req.params;
    const { title, address, latitude, longitude, is_default } = req.body;

    const existingAddress = await db('addresses')
      .where('id', id)
      .where('user_id', req.user.id)
      .first();

    if (!existingAddress) {
      return res.status(404).json({
        success: false,
        message: 'Address not found',
        message_ar: 'العنوان غير موجود'
      });
    }

    const updateData = { updated_at: new Date() };
    if (title !== undefined) updateData.title = title;
    if (address !== undefined) updateData.address = address;
    if (latitude !== undefined) updateData.latitude = latitude;
    if (longitude !== undefined) updateData.longitude = longitude;
    if (is_default !== undefined) updateData.is_default = is_default;

    // If this is set as default, unset other defaults
    if (is_default) {
      await db('addresses')
        .where('user_id', req.user.id)
        .where('id', '!=', id)
        .update({ is_default: false });
    }

    await db('addresses')
      .where('id', id)
      .where('user_id', req.user.id)
      .update(updateData);

    const updatedAddress = await db('addresses')
      .where('id', id)
      .first();

    res.json({
      success: true,
      message: 'Address updated successfully',
      message_ar: 'تم تحديث العنوان بنجاح',
      data: updatedAddress
    });
  } catch (error) {
    logger.error('Error updating address:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      message_ar: 'خطأ في الخادم الداخلي'
    });
  }
});

// Delete address
router.delete('/addresses/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const existingAddress = await db('addresses')
      .where('id', id)
      .where('user_id', req.user.id)
      .first();

    if (!existingAddress) {
      return res.status(404).json({
        success: false,
        message: 'Address not found',
        message_ar: 'العنوان غير موجود'
      });
    }

    await db('addresses')
      .where('id', id)
      .where('user_id', req.user.id)
      .del();

    res.json({
      success: true,
      message: 'Address deleted successfully',
      message_ar: 'تم حذف العنوان بنجاح'
    });
  } catch (error) {
    logger.error('Error deleting address:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      message_ar: 'خطأ في الخادم الداخلي'
    });
  }
});

module.exports = router;
