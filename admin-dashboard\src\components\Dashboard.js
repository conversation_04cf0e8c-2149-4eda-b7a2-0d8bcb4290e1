import React, { useState, useEffect } from 'react';
import { Row, Col, Card, Statistic, Table, Typography, Spin, Alert } from 'antd';
import {
  UserOutlined,
  ShopOutlined,
  CarOutlined,
  ShoppingOutlined,
  DollarOutlined,
} from '@ant-design/icons';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';
import axios from 'axios';

const { Title } = Typography;

const Dashboard = () => {
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState(null);
  const [error, setError] = useState(null);

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      const response = await axios.get('/api/admin/dashboard');
      if (response.data.success) {
        setStats(response.data.data);
      } else {
        setError('فشل في تحميل البيانات');
      }
    } catch (error) {
      setError('حدث خطأ أثناء تحميل البيانات');
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status) => {
    const colors = {
      pending: '#fa8c16',
      confirmed: '#1890ff',
      preparing: '#722ed1',
      on_way: '#13c2c2',
      delivered: '#52c41a',
      cancelled: '#ff4d4f',
    };
    return colors[status] || '#666';
  };

  const getStatusText = (status) => {
    const texts = {
      pending: 'في الانتظار',
      confirmed: 'مؤكد',
      preparing: 'قيد التحضير',
      on_way: 'في الطريق',
      delivered: 'تم التسليم',
      cancelled: 'ملغي',
    };
    return texts[status] || status;
  };

  const orderColumns = [
    {
      title: 'رقم الطلب',
      dataIndex: 'id',
      key: 'id',
    },
    {
      title: 'العميل',
      dataIndex: 'user_name',
      key: 'user_name',
    },
    {
      title: 'المتجر',
      dataIndex: 'store_name',
      key: 'store_name',
    },
    {
      title: 'المبلغ',
      dataIndex: 'total_amount',
      key: 'total_amount',
      render: (amount) => `${amount} ر.س`,
    },
    {
      title: 'الحالة',
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <span
          style={{
            color: getStatusColor(status),
            fontWeight: 500,
          }}
        >
          {getStatusText(status)}
        </span>
      ),
    },
    {
      title: 'التاريخ',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (date) => new Date(date).toLocaleDateString('ar-SA'),
    },
  ];

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
        <div style={{ marginTop: 16 }}>جاري تحميل البيانات...</div>
      </div>
    );
  }

  if (error) {
    return (
      <Alert
        message="خطأ"
        description={error}
        type="error"
        showIcon
        style={{ margin: '20px 0' }}
      />
    );
  }

  // Mock chart data for demonstration
  const chartData = [
    { name: 'يناير', orders: 65 },
    { name: 'فبراير', orders: 78 },
    { name: 'مارس', orders: 90 },
    { name: 'أبريل', orders: 81 },
    { name: 'مايو', orders: 95 },
    { name: 'يونيو', orders: 110 },
  ];

  return (
    <div>
      <Title level={2} style={{ marginBottom: 24 }}>
        لوحة التحكم
      </Title>

      {/* Statistics Cards */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={12} lg={6}>
          <Card className="dashboard-card">
            <Statistic
              title="إجمالي المستخدمين"
              value={stats?.totalUsers || 0}
              prefix={<UserOutlined style={{ color: '#1890ff' }} />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card className="dashboard-card">
            <Statistic
              title="إجمالي المتاجر"
              value={stats?.totalStores || 0}
              prefix={<ShopOutlined style={{ color: '#52c41a' }} />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card className="dashboard-card">
            <Statistic
              title="إجمالي الكباتن"
              value={stats?.totalCaptains || 0}
              prefix={<CarOutlined style={{ color: '#fa8c16' }} />}
              valueStyle={{ color: '#fa8c16' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card className="dashboard-card">
            <Statistic
              title="إجمالي الطلبات"
              value={stats?.totalOrders || 0}
              prefix={<ShoppingOutlined style={{ color: '#722ed1' }} />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
      </Row>

      {/* Revenue Card */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} lg={12}>
          <Card className="dashboard-card">
            <Statistic
              title="إجمالي الإيرادات"
              value={stats?.revenue || 0}
              prefix={<DollarOutlined style={{ color: '#13c2c2' }} />}
              suffix="ر.س"
              valueStyle={{ color: '#13c2c2' }}
            />
          </Card>
        </Col>
        <Col xs={24} lg={12}>
          <Card className="dashboard-card" title="الطلبات الشهرية">
            <ResponsiveContainer width="100%" height={200}>
              <LineChart data={chartData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <Tooltip />
                <Line type="monotone" dataKey="orders" stroke="#1890ff" strokeWidth={2} />
              </LineChart>
            </ResponsiveContainer>
          </Card>
        </Col>
      </Row>

      {/* Recent Orders */}
      <Card className="dashboard-card" title="الطلبات الأخيرة">
        <Table
          columns={orderColumns}
          dataSource={stats?.recentOrders || []}
          rowKey="id"
          pagination={false}
          scroll={{ x: 800 }}
        />
      </Card>
    </div>
  );
};

export default Dashboard;
