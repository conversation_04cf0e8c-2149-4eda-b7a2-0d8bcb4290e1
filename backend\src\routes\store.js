const express = require('express');
const { query, validationResult } = require('express-validator');
const db = require('../config/database');
const logger = require('../utils/logger');

const router = express.Router();

// Get all stores with pagination and filters
router.get('/', [
  query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100'),
  query('category').optional().isString().withMessage('Category must be a string'),
  query('search').optional().isString().withMessage('Search must be a string'),
  query('latitude').optional().isFloat().withMessage('Latitude must be a valid number'),
  query('longitude').optional().isFloat().withMessage('Longitude must be a valid number'),
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation error',
        message_ar: 'خطأ في التحقق من البيانات',
        errors: errors.array()
      });
    }

    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const offset = (page - 1) * limit;
    const { category, search, latitude, longitude } = req.query;

    let query = db('stores')
      .select(
        'stores.*',
        db.raw('COALESCE(AVG(reviews.rating), 0) as average_rating'),
        db.raw('COUNT(reviews.id) as review_count')
      )
      .leftJoin('reviews', 'stores.id', 'reviews.store_id')
      .where('stores.is_active', true)
      .groupBy('stores.id');

    // Apply filters
    if (category) {
      query = query.where('stores.category', category);
    }

    if (search) {
      query = query.where(function() {
        this.where('stores.name', 'ilike', `%${search}%`)
            .orWhere('stores.description', 'ilike', `%${search}%`);
      });
    }

    // Calculate distance if coordinates provided
    if (latitude && longitude) {
      query = query.select(
        db.raw(`
          (6371 * acos(
            cos(radians(?)) * cos(radians(stores.latitude)) *
            cos(radians(stores.longitude) - radians(?)) +
            sin(radians(?)) * sin(radians(stores.latitude))
          )) as distance
        `, [latitude, longitude, latitude])
      ).orderBy('distance');
    } else {
      query = query.orderBy('stores.created_at', 'desc');
    }

    // Get total count for pagination
    const totalQuery = db('stores')
      .count('* as count')
      .where('is_active', true);

    if (category) {
      totalQuery.where('category', category);
    }

    if (search) {
      totalQuery.where(function() {
        this.where('name', 'ilike', `%${search}%`)
            .orWhere('description', 'ilike', `%${search}%`);
      });
    }

    const [{ count: total }] = await totalQuery;
    const stores = await query.limit(limit).offset(offset);

    res.json({
      success: true,
      data: {
        stores,
        pagination: {
          page,
          limit,
          total: parseInt(total),
          pages: Math.ceil(total / limit)
        }
      }
    });
  } catch (error) {
    logger.error('Error fetching stores:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      message_ar: 'خطأ في الخادم الداخلي'
    });
  }
});

// Get store by ID with products
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const store = await db('stores')
      .select(
        'stores.*',
        db.raw('COALESCE(AVG(reviews.rating), 0) as average_rating'),
        db.raw('COUNT(reviews.id) as review_count')
      )
      .leftJoin('reviews', 'stores.id', 'reviews.store_id')
      .where('stores.id', id)
      .where('stores.is_active', true)
      .groupBy('stores.id')
      .first();

    if (!store) {
      return res.status(404).json({
        success: false,
        message: 'Store not found',
        message_ar: 'المتجر غير موجود'
      });
    }

    // Get store products
    const products = await db('products')
      .where('store_id', id)
      .where('is_available', true)
      .orderBy('category')
      .orderBy('name');

    // Get store reviews
    const reviews = await db('reviews')
      .select(
        'reviews.*',
        'users.name as user_name'
      )
      .join('users', 'reviews.user_id', 'users.id')
      .where('reviews.store_id', id)
      .orderBy('reviews.created_at', 'desc')
      .limit(10);

    res.json({
      success: true,
      data: {
        store,
        products,
        reviews
      }
    });
  } catch (error) {
    logger.error('Error fetching store details:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      message_ar: 'خطأ في الخادم الداخلي'
    });
  }
});

// Get store categories
router.get('/categories/list', async (req, res) => {
  try {
    const categories = await db('stores')
      .distinct('category')
      .where('is_active', true)
      .whereNotNull('category')
      .orderBy('category');

    res.json({
      success: true,
      data: categories.map(c => c.category)
    });
  } catch (error) {
    logger.error('Error fetching store categories:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      message_ar: 'خطأ في الخادم الداخلي'
    });
  }
});

// Get featured stores
router.get('/featured/list', async (req, res) => {
  try {
    const featuredStores = await db('stores')
      .select(
        'stores.*',
        db.raw('COALESCE(AVG(reviews.rating), 0) as average_rating'),
        db.raw('COUNT(reviews.id) as review_count')
      )
      .leftJoin('reviews', 'stores.id', 'reviews.store_id')
      .where('stores.is_active', true)
      .where('stores.is_featured', true)
      .groupBy('stores.id')
      .orderBy('stores.created_at', 'desc')
      .limit(10);

    res.json({
      success: true,
      data: featuredStores
    });
  } catch (error) {
    logger.error('Error fetching featured stores:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      message_ar: 'خطأ في الخادم الداخلي'
    });
  }
});

// Search stores
router.get('/search/query', [
  query('q').notEmpty().withMessage('Search query is required'),
  query('latitude').optional().isFloat().withMessage('Latitude must be a valid number'),
  query('longitude').optional().isFloat().withMessage('Longitude must be a valid number'),
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation error',
        message_ar: 'خطأ في التحقق من البيانات',
        errors: errors.array()
      });
    }

    const { q: searchQuery, latitude, longitude } = req.query;

    let query = db('stores')
      .select(
        'stores.*',
        db.raw('COALESCE(AVG(reviews.rating), 0) as average_rating'),
        db.raw('COUNT(reviews.id) as review_count')
      )
      .leftJoin('reviews', 'stores.id', 'reviews.store_id')
      .where('stores.is_active', true)
      .where(function() {
        this.where('stores.name', 'ilike', `%${searchQuery}%`)
            .orWhere('stores.description', 'ilike', `%${searchQuery}%`)
            .orWhere('stores.category', 'ilike', `%${searchQuery}%`);
      })
      .groupBy('stores.id');

    // Calculate distance if coordinates provided
    if (latitude && longitude) {
      query = query.select(
        db.raw(`
          (6371 * acos(
            cos(radians(?)) * cos(radians(stores.latitude)) *
            cos(radians(stores.longitude) - radians(?)) +
            sin(radians(?)) * sin(radians(stores.latitude))
          )) as distance
        `, [latitude, longitude, latitude])
      ).orderBy('distance');
    } else {
      query = query.orderBy('stores.created_at', 'desc');
    }

    const stores = await query.limit(20);

    res.json({
      success: true,
      data: stores
    });
  } catch (error) {
    logger.error('Error searching stores:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      message_ar: 'خطأ في الخادم الداخلي'
    });
  }
});

module.exports = router;
