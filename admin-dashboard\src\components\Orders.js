import React, { useState, useEffect } from 'react';
import { Table, Card, Tag, message } from 'antd';
import { ShoppingOutlined } from '@ant-design/icons';

const Orders = () => {
  const [orders, setOrders] = useState([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    // Mock data for demonstration
    const mockOrders = [
      {
        id: 1,
        user_name: 'أحمد محمد',
        store_name: 'مطعم البيت',
        total_amount: 85.50,
        status: 'delivered',
        created_at: new Date().toISOString(),
      },
      {
        id: 2,
        user_name: 'فاطمة علي',
        store_name: 'كافيه الأصدقاء',
        total_amount: 42.00,
        status: 'on_way',
        created_at: new Date().toISOString(),
      },
      {
        id: 3,
        user_name: 'محمد سالم',
        store_name: 'مطعم الشام',
        total_amount: 120.75,
        status: 'preparing',
        created_at: new Date().toISOString(),
      },
    ];
    setOrders(mockOrders);
  }, []);

  const getStatusColor = (status) => {
    const colors = {
      pending: 'orange',
      confirmed: 'blue',
      preparing: 'purple',
      on_way: 'cyan',
      delivered: 'green',
      cancelled: 'red',
    };
    return colors[status] || 'default';
  };

  const getStatusText = (status) => {
    const texts = {
      pending: 'في الانتظار',
      confirmed: 'مؤكد',
      preparing: 'قيد التحضير',
      on_way: 'في الطريق',
      delivered: 'تم التسليم',
      cancelled: 'ملغي',
    };
    return texts[status] || status;
  };

  const handleSearch = (value) => {
    setFilters(prev => ({ ...prev, search: value }));
    setPagination(prev => ({ ...prev, current: 1 }));
  };

  const handleStatusFilter = (value) => {
    setFilters(prev => ({ ...prev, status: value }));
    setPagination(prev => ({ ...prev, current: 1 }));
  };

  const columns = [
    {
      title: 'رقم الطلب',
      dataIndex: 'id',
      key: 'id',
      render: (id) => (
        <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
          <ShoppingOutlined />
          #{id}
        </div>
      ),
    },
    {
      title: 'العميل',
      dataIndex: 'user_name',
      key: 'user_name',
    },
    {
      title: 'المتجر',
      dataIndex: 'store_name',
      key: 'store_name',
    },
    {
      title: 'المبلغ الإجمالي',
      dataIndex: 'total_amount',
      key: 'total_amount',
      render: (amount) => `${amount} ر.س`,
      sorter: (a, b) => a.total_amount - b.total_amount,
    },
    {
      title: 'الحالة',
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <Tag color={getStatusColor(status)}>
          {getStatusText(status)}
        </Tag>
      ),
      filters: [
        { text: 'في الانتظار', value: 'pending' },
        { text: 'مؤكد', value: 'confirmed' },
        { text: 'قيد التحضير', value: 'preparing' },
        { text: 'في الطريق', value: 'on_way' },
        { text: 'تم التسليم', value: 'delivered' },
        { text: 'ملغي', value: 'cancelled' },
      ],
      onFilter: (value, record) => record.status === value,
    },
    {
      title: 'تاريخ الطلب',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (date) => new Date(date).toLocaleDateString('ar-SA'),
      sorter: (a, b) => new Date(a.created_at) - new Date(b.created_at),
    },
  ];

  return (
    <Card title="إدارة الطلبات">
      <Space style={{ marginBottom: 16, width: '100%', justifyContent: 'space-between' }}>
        <Space>
          <Input.Search
            placeholder="البحث في الطلبات..."
            allowClear
            enterButton={<SearchOutlined />}
            size="middle"
            onSearch={handleSearch}
            style={{ width: 300 }}
          />
          <Select
            placeholder="تصفية حسب الحالة"
            allowClear
            style={{ width: 200 }}
            onChange={handleStatusFilter}
          >
            <Option value="pending">في الانتظار</Option>
            <Option value="confirmed">مؤكد</Option>
            <Option value="preparing">قيد التحضير</Option>
            <Option value="on_way">في الطريق</Option>
            <Option value="delivered">تم التسليم</Option>
            <Option value="cancelled">ملغي</Option>
          </Select>
        </Space>
      </Space>

      <Table
        columns={columns}
        dataSource={orders}
        rowKey="id"
        loading={loading}
        pagination={{
          ...pagination,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) =>
            `${range[0]}-${range[1]} من ${total} طلب`,
          onChange: (page, pageSize) => {
            setPagination(prev => ({
              ...prev,
              current: page,
              pageSize,
            }));
          },
        }}
        scroll={{ x: 1000 }}
      />
    </Card>
  );
};

export default Orders;
