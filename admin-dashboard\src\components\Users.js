import React, { useState, useEffect } from 'react';
import { Table, Card, Button, Input, Space, Tag, message, Popconfirm } from 'antd';
import { SearchOutlined, UserOutlined } from '@ant-design/icons';
import axios from 'axios';

const Users = () => {
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0,
  });
  const [searchText, setSearchText] = useState('');

  useEffect(() => {
    fetchUsers();
  }, [pagination.current, pagination.pageSize]);

  const fetchUsers = async (search = '') => {
    setLoading(true);
    try {
      const response = await axios.get('/api/admin/users', {
        params: {
          page: pagination.current,
          limit: pagination.pageSize,
          search,
        },
      });

      if (response.data.success) {
        setUsers(response.data.data.users);
        setPagination(prev => ({
          ...prev,
          total: response.data.data.pagination.total,
        }));
      }
    } catch (error) {
      message.error('فشل في تحميل المستخدمين');
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = (value) => {
    setSearchText(value);
    setPagination(prev => ({ ...prev, current: 1 }));
    fetchUsers(value);
  };

  const toggleUserStatus = async (userId, currentStatus) => {
    try {
      const response = await axios.put(`/api/admin/users/${userId}/toggle-status`);
      if (response.data.success) {
        message.success(response.data.message_ar);
        fetchUsers(searchText);
      }
    } catch (error) {
      message.error('فشل في تغيير حالة المستخدم');
    }
  };

  const columns = [
    {
      title: 'الاسم',
      dataIndex: 'name',
      key: 'name',
      render: (name) => (
        <Space>
          <UserOutlined />
          {name}
        </Space>
      ),
    },
    {
      title: 'البريد الإلكتروني',
      dataIndex: 'email',
      key: 'email',
    },
    {
      title: 'رقم الهاتف',
      dataIndex: 'phone',
      key: 'phone',
    },
    {
      title: 'الحالة',
      dataIndex: 'is_active',
      key: 'is_active',
      render: (isActive) => (
        <Tag color={isActive ? 'green' : 'red'}>
          {isActive ? 'نشط' : 'غير نشط'}
        </Tag>
      ),
    },
    {
      title: 'تاريخ التسجيل',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (date) => new Date(date).toLocaleDateString('ar-SA'),
    },
    {
      title: 'الإجراءات',
      key: 'actions',
      render: (_, record) => (
        <Popconfirm
          title={`هل تريد ${record.is_active ? 'إلغاء تفعيل' : 'تفعيل'} هذا المستخدم؟`}
          onConfirm={() => toggleUserStatus(record.id, record.is_active)}
          okText="نعم"
          cancelText="لا"
        >
          <Button size="small" type={record.is_active ? 'default' : 'primary'}>
            {record.is_active ? 'إلغاء التفعيل' : 'تفعيل'}
          </Button>
        </Popconfirm>
      ),
    },
  ];

  return (
    <Card title="إدارة المستخدمين">
      <Space style={{ marginBottom: 16 }}>
        <Input.Search
          placeholder="البحث عن مستخدم..."
          allowClear
          enterButton={<SearchOutlined />}
          size="middle"
          onSearch={handleSearch}
          style={{ width: 300 }}
        />
      </Space>

      <Table
        columns={columns}
        dataSource={users}
        rowKey="id"
        loading={loading}
        pagination={{
          ...pagination,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) =>
            `${range[0]}-${range[1]} من ${total} مستخدم`,
          onChange: (page, pageSize) => {
            setPagination(prev => ({
              ...prev,
              current: page,
              pageSize,
            }));
          },
        }}
        scroll={{ x: 800 }}
      />
    </Card>
  );
};

export default Users;
