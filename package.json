{"name": "trago-delivery-app", "version": "1.0.0", "description": "Trago - تطبيق توصيل الطلبات الشامل", "main": "index.js", "scripts": {"dev": "concurrently \"npm run backend:dev\" \"npm run admin:dev\"", "backend:dev": "cd backend && npm run dev", "backend:start": "cd backend && npm start", "admin:dev": "cd admin-dashboard && npm start", "admin:build": "cd admin-dashboard && npm run build", "user-app:build": "cd user-app && flutter build apk", "captain-app:build": "cd captain-app && flutter build apk", "install:all": "npm run install:backend && npm run install:admin", "install:backend": "cd backend && npm install", "install:admin": "cd admin-dashboard && npm install", "test": "npm run test:backend", "test:backend": "cd backend && npm test"}, "keywords": ["delivery", "food", "restaurant", "mobile-app", "flutter", "nodejs", "react", "توصيل", "طعام", "مطاعم"], "author": "Trago Team", "license": "UNLICENSED", "devDependencies": {"concurrently": "^8.2.2"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "https://github.com/trago-app/trago-delivery"}, "dependencies": {"sqlite3": "^5.1.7"}}