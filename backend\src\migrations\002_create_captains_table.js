/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function(knex) {
  return knex.schema.createTable('captains', function(table) {
    table.string('id', 36).primary().defaultTo(knex.raw("(lower(hex(randomblob(4))) || '-' || lower(hex(randomblob(2))) || '-4' || substr(lower(hex(randomblob(2))),2) || '-' || substr('89ab',abs(random()) % 4 + 1, 1) || substr(lower(hex(randomblob(2))),2) || '-' || lower(hex(randomblob(6))))"));
    table.string('user_id', 36).notNullable().references('id').inTable('users').onDelete('CASCADE');
    table.string('license_number', 50).nullable();
    table.enum('vehicle_type', ['motorcycle', 'car', 'bicycle']).notNullable().defaultTo('motorcycle');
    table.string('vehicle_plate', 20).nullable();
    table.string('vehicle_model', 50).nullable();
    table.string('vehicle_color', 30).nullable();
    table.boolean('is_approved').defaultTo(false);
    table.enum('status', ['offline', 'online', 'busy']).defaultTo('offline');
    table.text('current_location').nullable(); // Store as JSON string for SQLite
    table.decimal('rating', 3, 2).defaultTo(0);
    table.integer('total_orders').defaultTo(0);
    table.integer('completed_orders').defaultTo(0);
    table.integer('cancelled_orders').defaultTo(0);
    table.decimal('total_earnings', 10, 2).defaultTo(0);
    table.json('documents').nullable(); // Store document URLs
    table.timestamp('approved_at').nullable();
    table.string('approved_by', 36).nullable().references('id').inTable('users');
    table.timestamps(true, true);
    
    // Indexes
    table.index(['user_id']);
    table.index(['is_approved']);
    table.index(['status']);
    table.index(['rating']);
    table.index(['vehicle_type']);
  });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function(knex) {
  return knex.schema.dropTable('captains');
};
