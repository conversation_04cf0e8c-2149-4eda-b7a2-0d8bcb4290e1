import React, { useState, useEffect } from 'react';
import { Table, Card, Button, Tag, message, Popconfirm, Rate } from 'antd';
import { ShopOutlined } from '@ant-design/icons';
import axios from 'axios';

const Stores = () => {
  const [stores, setStores] = useState([]);
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0,
  });

  useEffect(() => {
    fetchStores();
  }, [pagination.current, pagination.pageSize]);

  const fetchStores = async () => {
    setLoading(true);
    try {
      const response = await axios.get('/api/admin/stores', {
        params: {
          page: pagination.current,
          limit: pagination.pageSize,
        },
      });

      if (response.data.success) {
        setStores(response.data.data.stores);
        setPagination(prev => ({
          ...prev,
          total: response.data.data.pagination.total,
        }));
      }
    } catch (error) {
      message.error('فشل في تحميل المتاجر');
    } finally {
      setLoading(false);
    }
  };

  const toggleStoreStatus = async (storeId) => {
    try {
      const response = await axios.put(`/api/admin/stores/${storeId}/toggle-status`);
      if (response.data.success) {
        message.success(response.data.message_ar);
        fetchStores();
      }
    } catch (error) {
      message.error('فشل في تغيير حالة المتجر');
    }
  };

  const columns = [
    {
      title: 'اسم المتجر',
      dataIndex: 'name',
      key: 'name',
      render: (name) => (
        <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
          <ShopOutlined />
          {name}
        </div>
      ),
    },
    {
      title: 'الفئة',
      dataIndex: 'category',
      key: 'category',
      render: (category) => <Tag color="blue">{category}</Tag>,
    },
    {
      title: 'البريد الإلكتروني',
      dataIndex: 'email',
      key: 'email',
    },
    {
      title: 'رقم الهاتف',
      dataIndex: 'phone',
      key: 'phone',
    },
    {
      title: 'التقييم',
      dataIndex: 'average_rating',
      key: 'average_rating',
      render: (rating, record) => (
        <div>
          <Rate disabled defaultValue={parseFloat(rating)} allowHalf />
          <div style={{ fontSize: 12, color: '#666' }}>
            ({record.review_count} تقييم)
          </div>
        </div>
      ),
    },
    {
      title: 'رسوم التوصيل',
      dataIndex: 'delivery_fee',
      key: 'delivery_fee',
      render: (fee) => `${fee} ر.س`,
    },
    {
      title: 'الحالة',
      dataIndex: 'is_active',
      key: 'is_active',
      render: (isActive) => (
        <Tag color={isActive ? 'green' : 'red'}>
          {isActive ? 'نشط' : 'غير نشط'}
        </Tag>
      ),
    },
    {
      title: 'تاريخ الإنشاء',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (date) => new Date(date).toLocaleDateString('ar-SA'),
    },
    {
      title: 'الإجراءات',
      key: 'actions',
      render: (_, record) => (
        <Popconfirm
          title={`هل تريد ${record.is_active ? 'إلغاء تفعيل' : 'تفعيل'} هذا المتجر؟`}
          onConfirm={() => toggleStoreStatus(record.id)}
          okText="نعم"
          cancelText="لا"
        >
          <Button size="small" type={record.is_active ? 'default' : 'primary'}>
            {record.is_active ? 'إلغاء التفعيل' : 'تفعيل'}
          </Button>
        </Popconfirm>
      ),
    },
  ];

  return (
    <Card title="إدارة المتاجر">
      <Table
        columns={columns}
        dataSource={stores}
        rowKey="id"
        loading={loading}
        pagination={{
          ...pagination,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) =>
            `${range[0]}-${range[1]} من ${total} متجر`,
          onChange: (page, pageSize) => {
            setPagination(prev => ({
              ...prev,
              current: page,
              pageSize,
            }));
          },
        }}
        scroll={{ x: 1000 }}
      />
    </Card>
  );
};

export default Stores;
