import 'package:flutter/foundation.dart';
import '../utils/logger.dart';

class AppConfig {
  // Environment
  static const String environment =
      String.fromEnvironment('ENVIRONMENT', defaultValue: 'development');
  static bool get isDevelopment => environment == 'development';
  static bool get isProduction => environment == 'production';
  static bool get isStaging => environment == 'staging';

  // API Configuration
  static String get baseUrl {
    switch (environment) {
      case 'production':
        return 'https://api.trago-app.com';
      case 'staging':
        return 'https://staging-api.trago-app.com';
      default:
        return 'http://localhost:3000';
    }
  }

  static String get apiUrl => '$baseUrl/api';
  static String get socketUrl => baseUrl;

  // Google Maps API Key
  static const String googleMapsApiKey = String.fromEnvironment(
    'GOOGLE_MAPS_API_KEY',
    defaultValue: 'your-google-maps-api-key',
  );

  // Firebase Configuration
  static const String firebaseProjectId = String.fromEnvironment(
    'FIREBASE_PROJECT_ID',
    defaultValue: 'trago-app',
  );

  // Payment Configuration
  static const String vodafoneCashMerchantId = String.fromEnvironment(
    'VODAFONE_CASH_MERCHANT_ID',
    defaultValue: '',
  );

  static const String fawryMerchantCode = String.fromEnvironment(
    'FAWRY_MERCHANT_CODE',
    defaultValue: '',
  );

  static const String orangeCashMerchantId = String.fromEnvironment(
    'ORANGE_CASH_MERCHANT_ID',
    defaultValue: '',
  );

  static const String etisalatCashMerchantId = String.fromEnvironment(
    'ETISALAT_CASH_MERCHANT_ID',
    defaultValue: '',
  );

  // Feature Flags
  static const bool enableBiometricAuth = bool.fromEnvironment(
    'ENABLE_BIOMETRIC_AUTH',
    defaultValue: true,
  );

  static const bool enableDarkMode = bool.fromEnvironment(
    'ENABLE_DARK_MODE',
    defaultValue: true,
  );

  static const bool enableNotifications = bool.fromEnvironment(
    'ENABLE_NOTIFICATIONS',
    defaultValue: true,
  );

  static const bool enableLocationTracking = bool.fromEnvironment(
    'ENABLE_LOCATION_TRACKING',
    defaultValue: true,
  );

  static const bool enableCrashReporting = bool.fromEnvironment(
    'ENABLE_CRASH_REPORTING',
    defaultValue: true,
  );

  static const bool enableAnalytics = bool.fromEnvironment(
    'ENABLE_ANALYTICS',
    defaultValue: true,
  );

  // Debug Configuration
  static bool get enableLogging => kDebugMode || isDevelopment;
  static bool get enableNetworkLogging => kDebugMode || isDevelopment;

  // Cache Configuration
  static const Duration shortCacheDuration = Duration(minutes: 5);
  static const Duration mediumCacheDuration = Duration(minutes: 30);
  static const Duration longCacheDuration = Duration(hours: 24);

  // Network Configuration
  static const Duration connectionTimeout = Duration(seconds: 30);
  static const Duration receiveTimeout = Duration(seconds: 30);
  static const Duration sendTimeout = Duration(seconds: 30);

  // Pagination Configuration
  static const int defaultPageSize = 20;
  static const int maxPageSize = 100;

  // File Upload Configuration
  static const int maxImageSize = 5 * 1024 * 1024; // 5MB
  static const int maxImagesCount = 5;
  static const List<String> allowedImageTypes = ['jpg', 'jpeg', 'png', 'webp'];

  // Location Configuration
  static const double defaultLatitude = 30.0444; // Cairo
  static const double defaultLongitude = 31.2357; // Cairo
  static const double defaultZoomLevel = 15.0;
  static const double minZoomLevel = 10.0;
  static const double maxZoomLevel = 20.0;
  static const double searchRadius = 10.0; // km
  static const double maxDeliveryDistance = 20.0; // km

  // Order Configuration
  static const double defaultDeliveryFee = 15.0;
  static const double minOrderAmount = 10.0;
  static const double maxOrderAmount = 1000.0;
  static const int maxOrderItems = 50;
  static const int deliveryTimeEstimate = 30; // minutes

  // Rating Configuration
  static const int minRating = 1;
  static const int maxRating = 5;
  static const double defaultRating = 0.0;

  // Validation Configuration
  static const int phoneMinLength = 11;
  static const int phoneMaxLength = 11;
  static const int passwordMinLength = 8;
  static const int passwordMaxLength = 50;
  static const int nameMinLength = 2;
  static const int nameMaxLength = 50;
  static const int otpLength = 6;

  // Animation Configuration
  static const Duration shortAnimationDuration = Duration(milliseconds: 200);
  static const Duration mediumAnimationDuration = Duration(milliseconds: 300);
  static const Duration longAnimationDuration = Duration(milliseconds: 500);

  // Social Media Links
  static const String facebookUrl = 'https://facebook.com/trago';
  static const String twitterUrl = 'https://twitter.com/trago';
  static const String instagramUrl = 'https://instagram.com/trago';
  static const String whatsappUrl = 'https://wa.me/201234567890';

  // Support Information
  static const String supportEmail = '<EMAIL>';
  static const String supportPhone = '+201234567890';
  static const String privacyPolicyUrl = 'https://trago-app.com/privacy';
  static const String termsOfServiceUrl = 'https://trago-app.com/terms';

  // App Store Links
  static const String playStoreUrl =
      'https://play.google.com/store/apps/details?id=com.trago.userapp';
  static const String appStoreUrl =
      'https://apps.apple.com/app/trago/id123456789';

  // Utility Methods
  static String getEnvironmentName() {
    switch (environment) {
      case 'production':
        return 'Production';
      case 'staging':
        return 'Staging';
      default:
        return 'Development';
    }
  }

  static Map<String, dynamic> getConfigInfo() {
    return {
      'environment': environment,
      'baseUrl': baseUrl,
      'apiUrl': apiUrl,
      'socketUrl': socketUrl,
      'enableLogging': enableLogging,
      'enableNetworkLogging': enableNetworkLogging,
      'enableBiometricAuth': enableBiometricAuth,
      'enableDarkMode': enableDarkMode,
      'enableNotifications': enableNotifications,
      'enableLocationTracking': enableLocationTracking,
      'enableCrashReporting': enableCrashReporting,
      'enableAnalytics': enableAnalytics,
    };
  }

  static void printConfigInfo() {
    if (enableLogging) {
      AppLogger.config('=== Trago App Configuration ===');
      AppLogger.config('Environment: ${getEnvironmentName()}');
      AppLogger.config('Base URL: $baseUrl');
      AppLogger.config('API URL: $apiUrl');
      AppLogger.config('Socket URL: $socketUrl');
      AppLogger.config('Debug Mode: $kDebugMode');
      AppLogger.config('Logging Enabled: $enableLogging');
      AppLogger.config('Network Logging: $enableNetworkLogging');
      AppLogger.config('===============================');
    }
  }
}
