const logger = require('../utils/logger');

// In-memory cache as Redis fallback
const memoryCache = new Map();
const hashCache = new Map();
const listCache = new Map();

let isRedisAvailable = false;
let client = null;

// Try to connect to Redis if available
(async () => {
  try {
    const { createClient } = require('redis');
    client = createClient({
      url: process.env.REDIS_URL || 'redis://localhost:6379',
      socket: {
        connectTimeout: 5000,
        lazyConnect: true
      }
    });

    client.on('connect', () => {
      logger.info('✅ Redis client connected');
      isRedisAvailable = true;
    });

    client.on('error', (err) => {
      logger.warn('⚠️ Redis not available, using memory cache:', err.message);
      isRedisAvailable = false;
    });

    await client.connect();
  } catch (error) {
    logger.warn('⚠️ Redis not available, using memory cache fallback');
    isRedisAvailable = false;
  }
})();

// Redis utility functions with memory fallback
const redis = {
  // Get value
  get: async (key) => {
    try {
      if (isRedisAvailable && client) {
        return await client.get(key);
      }
      return memoryCache.get(key) || null;
    } catch (error) {
      logger.error('Cache GET error:', error);
      return memoryCache.get(key) || null;
    }
  },

  // Set value with optional expiration
  set: async (key, value, expireInSeconds = null) => {
    try {
      if (isRedisAvailable && client) {
        if (expireInSeconds) {
          return await client.setEx(key, expireInSeconds, value);
        }
        return await client.set(key, value);
      }

      // Memory cache fallback
      memoryCache.set(key, value);
      if (expireInSeconds) {
        setTimeout(() => memoryCache.delete(key), expireInSeconds * 1000);
      }
      return 'OK';
    } catch (error) {
      logger.error('Cache SET error:', error);
      memoryCache.set(key, value);
      if (expireInSeconds) {
        setTimeout(() => memoryCache.delete(key), expireInSeconds * 1000);
      }
      return 'OK';
    }
  },

  // Delete key
  del: async (key) => {
    try {
      if (isRedisAvailable && client) {
        return await client.del(key);
      }
      return memoryCache.delete(key) ? 1 : 0;
    } catch (error) {
      logger.error('Cache DEL error:', error);
      return memoryCache.delete(key) ? 1 : 0;
    }
  },

  // Check if key exists
  exists: async (key) => {
    try {
      if (isRedisAvailable && client) {
        return await client.exists(key);
      }
      return memoryCache.has(key) ? 1 : 0;
    } catch (error) {
      logger.error('Cache EXISTS error:', error);
      return memoryCache.has(key) ? 1 : 0;
    }
  },

  // Set expiration
  expire: async (key, seconds) => {
    try {
      if (isRedisAvailable && client) {
        return await client.expire(key, seconds);
      }
      if (memoryCache.has(key)) {
        setTimeout(() => memoryCache.delete(key), seconds * 1000);
        return 1;
      }
      return 0;
    } catch (error) {
      logger.error('Cache EXPIRE error:', error);
      if (memoryCache.has(key)) {
        setTimeout(() => memoryCache.delete(key), seconds * 1000);
        return 1;
      }
      return 0;
    }
  },

  // Hash operations
  hset: async (key, field, value) => {
    try {
      if (isRedisAvailable && client) {
        return await client.hSet(key, field, value);
      }
      if (!hashCache.has(key)) {
        hashCache.set(key, new Map());
      }
      hashCache.get(key).set(field, value);
      return 1;
    } catch (error) {
      logger.error('Cache HSET error:', error);
      if (!hashCache.has(key)) {
        hashCache.set(key, new Map());
      }
      hashCache.get(key).set(field, value);
      return 1;
    }
  },

  hget: async (key, field) => {
    try {
      if (isRedisAvailable && client) {
        return await client.hGet(key, field);
      }
      return hashCache.has(key) ? hashCache.get(key).get(field) || null : null;
    } catch (error) {
      logger.error('Cache HGET error:', error);
      return hashCache.has(key) ? hashCache.get(key).get(field) || null : null;
    }
  },

  hgetall: async (key) => {
    try {
      if (isRedisAvailable && client) {
        return await client.hGetAll(key);
      }
      if (!hashCache.has(key)) return {};
      const result = {};
      for (const [field, value] of hashCache.get(key)) {
        result[field] = value;
      }
      return result;
    } catch (error) {
      logger.error('Cache HGETALL error:', error);
      if (!hashCache.has(key)) return {};
      const result = {};
      for (const [field, value] of hashCache.get(key)) {
        result[field] = value;
      }
      return result;
    }
  },

  // List operations
  lpush: async (key, value) => {
    try {
      if (isRedisAvailable && client) {
        return await client.lPush(key, value);
      }
      if (!listCache.has(key)) {
        listCache.set(key, []);
      }
      listCache.get(key).unshift(value);
      return listCache.get(key).length;
    } catch (error) {
      logger.error('Cache LPUSH error:', error);
      if (!listCache.has(key)) {
        listCache.set(key, []);
      }
      listCache.get(key).unshift(value);
      return listCache.get(key).length;
    }
  },

  rpop: async (key) => {
    try {
      if (isRedisAvailable && client) {
        return await client.rPop(key);
      }
      return listCache.has(key) ? listCache.get(key).pop() || null : null;
    } catch (error) {
      logger.error('Cache RPOP error:', error);
      return listCache.has(key) ? listCache.get(key).pop() || null : null;
    }
  },

  // Ping cache
  ping: async () => {
    try {
      if (isRedisAvailable && client) {
        return await client.ping();
      }
      return 'PONG';
    } catch (error) {
      logger.error('Cache PING error:', error);
      return 'PONG';
    }
  },

  // Close connection
  quit: async () => {
    try {
      if (client) {
        await client.quit();
      }
    } catch (error) {
      logger.error('Cache QUIT error:', error);
    }
  }
};

module.exports = redis;
