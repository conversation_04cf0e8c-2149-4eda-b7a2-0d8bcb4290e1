const knex = require('knex');
const logger = require('../utils/logger');

const config = {
  client: 'sqlite3',
  connection: {
    filename: './database/trago_dev.db'
  },
  useNullAsDefault: true,
  pool: {
    min: 2,
    max: 10,
    acquireTimeoutMillis: 30000,
    createTimeoutMillis: 30000,
    destroyTimeoutMillis: 5000,
    idleTimeoutMillis: 30000,
    reapIntervalMillis: 1000,
    createRetryIntervalMillis: 100,
    propagateCreateError: false
  },
  migrations: {
    directory: './src/migrations',
    tableName: 'knex_migrations'
  },
  seeds: {
    directory: './src/seeds'
  }
};

const db = knex(config);

// Test connection
db.raw('SELECT 1')
  .then(() => {
    logger.info('✅ Database connection established');
  })
  .catch((err) => {
    logger.error('❌ Database connection failed:', err.message);
  });

module.exports = db;
