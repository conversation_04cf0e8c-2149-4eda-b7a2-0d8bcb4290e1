const express = require('express');
const { body, query, validationResult } = require('express-validator');
const db = require('../config/database');
const logger = require('../utils/logger');

const router = express.Router();

// Get user orders
router.get('/', [
  query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100'),
  query('status').optional().isIn(['pending', 'confirmed', 'preparing', 'on_way', 'delivered', 'cancelled']).withMessage('Invalid status'),
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation error',
        message_ar: 'خطأ في التحقق من البيانات',
        errors: errors.array()
      });
    }

    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const offset = (page - 1) * limit;
    const { status } = req.query;

    let query = db('orders')
      .select(
        'orders.*',
        'stores.name as store_name',
        'stores.image as store_image',
        'addresses.address as delivery_address'
      )
      .join('stores', 'orders.store_id', 'stores.id')
      .leftJoin('addresses', 'orders.address_id', 'addresses.id')
      .where('orders.user_id', req.user.id);

    if (status) {
      query = query.where('orders.status', status);
    }

    const orders = await query
      .orderBy('orders.created_at', 'desc')
      .limit(limit)
      .offset(offset);

    // Get order items for each order
    for (let order of orders) {
      order.items = await db('order_items')
        .select(
          'order_items.*',
          'products.name as product_name',
          'products.image as product_image'
        )
        .join('products', 'order_items.product_id', 'products.id')
        .where('order_items.order_id', order.id);
    }

    // Get total count
    let countQuery = db('orders')
      .count('* as count')
      .where('user_id', req.user.id);

    if (status) {
      countQuery = countQuery.where('status', status);
    }

    const [{ count: total }] = await countQuery;

    res.json({
      success: true,
      data: {
        orders,
        pagination: {
          page,
          limit,
          total: parseInt(total),
          pages: Math.ceil(total / limit)
        }
      }
    });
  } catch (error) {
    logger.error('Error fetching user orders:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      message_ar: 'خطأ في الخادم الداخلي'
    });
  }
});

// Get order by ID
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const order = await db('orders')
      .select(
        'orders.*',
        'stores.name as store_name',
        'stores.image as store_image',
        'stores.phone as store_phone',
        'addresses.address as delivery_address',
        'addresses.latitude as delivery_latitude',
        'addresses.longitude as delivery_longitude',
        'captains.name as captain_name',
        'captains.phone as captain_phone'
      )
      .join('stores', 'orders.store_id', 'stores.id')
      .leftJoin('addresses', 'orders.address_id', 'addresses.id')
      .leftJoin('captains', 'orders.captain_id', 'captains.id')
      .where('orders.id', id)
      .where('orders.user_id', req.user.id)
      .first();

    if (!order) {
      return res.status(404).json({
        success: false,
        message: 'Order not found',
        message_ar: 'الطلب غير موجود'
      });
    }

    // Get order items
    order.items = await db('order_items')
      .select(
        'order_items.*',
        'products.name as product_name',
        'products.image as product_image',
        'products.description as product_description'
      )
      .join('products', 'order_items.product_id', 'products.id')
      .where('order_items.order_id', order.id);

    res.json({
      success: true,
      data: order
    });
  } catch (error) {
    logger.error('Error fetching order details:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      message_ar: 'خطأ في الخادم الداخلي'
    });
  }
});

// Create new order
router.post('/', [
  body('store_id').isInt().withMessage('Store ID is required'),
  body('address_id').isInt().withMessage('Address ID is required'),
  body('items').isArray({ min: 1 }).withMessage('Order items are required'),
  body('items.*.product_id').isInt().withMessage('Product ID is required'),
  body('items.*.quantity').isInt({ min: 1 }).withMessage('Quantity must be at least 1'),
  body('payment_method').isIn(['cash', 'card']).withMessage('Invalid payment method'),
  body('notes').optional().isString().withMessage('Notes must be a string'),
], async (req, res) => {
  const trx = await db.transaction();

  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      await trx.rollback();
      return res.status(400).json({
        success: false,
        message: 'Validation error',
        message_ar: 'خطأ في التحقق من البيانات',
        errors: errors.array()
      });
    }

    const { store_id, address_id, items, payment_method, notes } = req.body;

    // Verify store exists and is active
    const store = await trx('stores')
      .where('id', store_id)
      .where('is_active', true)
      .first();

    if (!store) {
      await trx.rollback();
      return res.status(404).json({
        success: false,
        message: 'Store not found or inactive',
        message_ar: 'المتجر غير موجود أو غير نشط'
      });
    }

    // Verify address belongs to user
    const address = await trx('addresses')
      .where('id', address_id)
      .where('user_id', req.user.id)
      .first();

    if (!address) {
      await trx.rollback();
      return res.status(404).json({
        success: false,
        message: 'Address not found',
        message_ar: 'العنوان غير موجود'
      });
    }

    // Calculate total amount
    let totalAmount = 0;
    const orderItems = [];

    for (const item of items) {
      const product = await trx('products')
        .where('id', item.product_id)
        .where('store_id', store_id)
        .where('is_available', true)
        .first();

      if (!product) {
        await trx.rollback();
        return res.status(404).json({
          success: false,
          message: `Product with ID ${item.product_id} not found or unavailable`,
          message_ar: `المنتج رقم ${item.product_id} غير موجود أو غير متاح`
        });
      }

      const itemTotal = product.price * item.quantity;
      totalAmount += itemTotal;

      orderItems.push({
        product_id: item.product_id,
        quantity: item.quantity,
        price: product.price,
        total: itemTotal
      });
    }

    // Add delivery fee
    const deliveryFee = store.delivery_fee || 0;
    totalAmount += deliveryFee;

    // Create order
    const [orderId] = await trx('orders').insert({
      user_id: req.user.id,
      store_id,
      address_id,
      total_amount: totalAmount,
      delivery_fee: deliveryFee,
      payment_method,
      notes,
      status: 'pending',
      created_at: new Date(),
      updated_at: new Date()
    }).returning('id');

    // Create order items
    for (const item of orderItems) {
      await trx('order_items').insert({
        order_id: orderId,
        ...item,
        created_at: new Date(),
        updated_at: new Date()
      });
    }

    await trx.commit();

    // Get the created order with details
    const newOrder = await db('orders')
      .select(
        'orders.*',
        'stores.name as store_name',
        'addresses.address as delivery_address'
      )
      .join('stores', 'orders.store_id', 'stores.id')
      .join('addresses', 'orders.address_id', 'addresses.id')
      .where('orders.id', orderId)
      .first();

    // Emit socket event to store
    const io = req.app.get('io');
    io.to(`store_${store_id}`).emit('new_order', {
      order: newOrder,
      message: 'New order received',
      message_ar: 'تم استلام طلب جديد'
    });

    res.status(201).json({
      success: true,
      message: 'Order created successfully',
      message_ar: 'تم إنشاء الطلب بنجاح',
      data: newOrder
    });
  } catch (error) {
    await trx.rollback();
    logger.error('Error creating order:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      message_ar: 'خطأ في الخادم الداخلي'
    });
  }
});

// Cancel order
router.put('/:id/cancel', async (req, res) => {
  try {
    const { id } = req.params;

    const order = await db('orders')
      .where('id', id)
      .where('user_id', req.user.id)
      .first();

    if (!order) {
      return res.status(404).json({
        success: false,
        message: 'Order not found',
        message_ar: 'الطلب غير موجود'
      });
    }

    if (!['pending', 'confirmed'].includes(order.status)) {
      return res.status(400).json({
        success: false,
        message: 'Order cannot be cancelled at this stage',
        message_ar: 'لا يمكن إلغاء الطلب في هذه المرحلة'
      });
    }

    await db('orders')
      .where('id', id)
      .update({
        status: 'cancelled',
        updated_at: new Date()
      });

    // Emit socket event
    const io = req.app.get('io');
    io.to(`store_${order.store_id}`).emit('order_cancelled', {
      order_id: id,
      message: 'Order cancelled by customer',
      message_ar: 'تم إلغاء الطلب من قبل العميل'
    });

    res.json({
      success: true,
      message: 'Order cancelled successfully',
      message_ar: 'تم إلغاء الطلب بنجاح'
    });
  } catch (error) {
    logger.error('Error cancelling order:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      message_ar: 'خطأ في الخادم الداخلي'
    });
  }
});

// Rate order
router.post('/:id/rate', [
  body('rating').isInt({ min: 1, max: 5 }).withMessage('Rating must be between 1 and 5'),
  body('comment').optional().isString().withMessage('Comment must be a string'),
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation error',
        message_ar: 'خطأ في التحقق من البيانات',
        errors: errors.array()
      });
    }

    const { id } = req.params;
    const { rating, comment } = req.body;

    const order = await db('orders')
      .where('id', id)
      .where('user_id', req.user.id)
      .where('status', 'delivered')
      .first();

    if (!order) {
      return res.status(404).json({
        success: false,
        message: 'Order not found or not delivered',
        message_ar: 'الطلب غير موجود أو لم يتم تسليمه'
      });
    }

    // Check if already rated
    const existingReview = await db('reviews')
      .where('order_id', id)
      .where('user_id', req.user.id)
      .first();

    if (existingReview) {
      return res.status(400).json({
        success: false,
        message: 'Order already rated',
        message_ar: 'تم تقييم الطلب مسبقاً'
      });
    }

    // Create review
    await db('reviews').insert({
      user_id: req.user.id,
      store_id: order.store_id,
      order_id: id,
      rating,
      comment,
      created_at: new Date(),
      updated_at: new Date()
    });

    res.json({
      success: true,
      message: 'Order rated successfully',
      message_ar: 'تم تقييم الطلب بنجاح'
    });
  } catch (error) {
    logger.error('Error rating order:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      message_ar: 'خطأ في الخادم الداخلي'
    });
  }
});

module.exports = router;
