/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function(knex) {
  return knex.schema.createTable('products', function(table) {
    table.string('id', 36).primary().defaultTo(knex.raw("(lower(hex(randomblob(4))) || '-' || lower(hex(randomblob(2))) || '-4' || substr(lower(hex(randomblob(2))),2) || '-' || substr('89ab',abs(random()) % 4 + 1, 1) || substr(lower(hex(randomblob(2))),2) || '-' || lower(hex(randomblob(6))))"));
    table.string('store_id', 36).notNullable().references('id').inTable('stores').onDelete('CASCADE');
    table.string('name', 100).notNullable();
    table.text('description').nullable();
    table.json('images').nullable(); // Array of image URLs
    table.decimal('price', 8, 2).notNullable();
    table.decimal('discount_price', 8, 2).nullable();
    table.string('category', 50).nullable();
    table.boolean('is_available').defaultTo(true);
    table.integer('stock_quantity').nullable(); // null means unlimited
    table.json('options').nullable(); // Array of options like size, color, etc.
    table.json('nutritional_info').nullable(); // For food items
    table.decimal('preparation_time', 5, 2).nullable(); // in minutes
    table.decimal('rating', 3, 2).defaultTo(0);
    table.integer('total_reviews').defaultTo(0);
    table.integer('total_orders').defaultTo(0);
    table.timestamps(true, true);
    
    // Indexes
    table.index(['store_id']);
    table.index(['name']);
    table.index(['category']);
    table.index(['is_available']);
    table.index(['price']);
    table.index(['rating']);
  });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function(knex) {
  return knex.schema.dropTable('products');
};
