/// تعريف أحجام ومسافات التطبيق
class AppSizes {
  AppSizes._();

  // المسافات (Spacing)
  static const double spacingXs = 4.0;
  static const double spacingSm = 8.0;
  static const double spacingMd = 16.0;
  static const double spacingLg = 24.0;
  static const double spacingXl = 32.0;
  static const double spacingXxl = 48.0;

  // الحشو (Padding)
  static const double paddingXs = 4.0;
  static const double paddingSm = 8.0;
  static const double paddingMd = 16.0;
  static const double paddingLg = 24.0;
  static const double paddingXl = 32.0;
  static const double paddingXxl = 48.0;

  // الهوامش (Margins)
  static const double marginXs = 4.0;
  static const double marginSm = 8.0;
  static const double marginMd = 16.0;
  static const double marginLg = 24.0;
  static const double marginXl = 32.0;
  static const double marginXxl = 48.0;

  // نصف القطر (Border Radius)
  static const double radiusXs = 4.0;
  static const double radiusSm = 8.0;
  static const double radiusMd = 12.0;
  static const double radiusLg = 16.0;
  static const double radiusXl = 24.0;
  static const double radiusXxl = 32.0;
  static const double radiusRound = 50.0;

  // سماكة الحدود (Border Width)
  static const double borderThin = 1.0;
  static const double borderMedium = 2.0;
  static const double borderThick = 3.0;

  // ارتفاع العناصر (Heights)
  static const double buttonHeight = 48.0;
  static const double buttonHeightSmall = 36.0;
  static const double buttonHeightLarge = 56.0;
  
  static const double inputHeight = 48.0;
  static const double inputHeightSmall = 36.0;
  static const double inputHeightLarge = 56.0;
  
  static const double appBarHeight = 56.0;
  static const double tabBarHeight = 48.0;
  static const double bottomNavHeight = 60.0;
  
  static const double cardHeight = 120.0;
  static const double cardHeightSmall = 80.0;
  static const double cardHeightLarge = 160.0;

  // عرض العناصر (Widths)
  static const double buttonMinWidth = 88.0;
  static const double dialogMaxWidth = 400.0;
  static const double sideMenuWidth = 280.0;

  // أحجام الأيقونات (Icon Sizes)
  static const double iconXs = 16.0;
  static const double iconSm = 20.0;
  static const double iconMd = 24.0;
  static const double iconLg = 32.0;
  static const double iconXl = 48.0;
  static const double iconXxl = 64.0;

  // أحجام الصور (Image Sizes)
  static const double imageXs = 32.0;
  static const double imageSm = 48.0;
  static const double imageMd = 64.0;
  static const double imageLg = 96.0;
  static const double imageXl = 128.0;
  static const double imageXxl = 192.0;

  // أحجام الصور المصغرة (Thumbnail Sizes)
  static const double thumbnailXs = 24.0;
  static const double thumbnailSm = 32.0;
  static const double thumbnailMd = 48.0;
  static const double thumbnailLg = 64.0;

  // أحجام الأفاتار (Avatar Sizes)
  static const double avatarXs = 24.0;
  static const double avatarSm = 32.0;
  static const double avatarMd = 48.0;
  static const double avatarLg = 64.0;
  static const double avatarXl = 96.0;

  // أحجام النص (Text Sizes)
  static const double textXs = 10.0;
  static const double textSm = 12.0;
  static const double textMd = 14.0;
  static const double textLg = 16.0;
  static const double textXl = 18.0;
  static const double textXxl = 20.0;
  static const double textTitle = 24.0;
  static const double textHeading = 32.0;

  // ارتفاع الخط (Line Heights)
  static const double lineHeightTight = 1.2;
  static const double lineHeightNormal = 1.4;
  static const double lineHeightRelaxed = 1.6;
  static const double lineHeightLoose = 1.8;

  // الظلال (Elevation)
  static const double elevationNone = 0.0;
  static const double elevationLow = 2.0;
  static const double elevationMedium = 4.0;
  static const double elevationHigh = 8.0;
  static const double elevationVeryHigh = 16.0;

  // أحجام خاصة بالتطبيق
  static const double productCardHeight = 200.0;
  static const double storeCardHeight = 120.0;
  static const double orderItemHeight = 80.0;
  static const double categoryItemWidth = 100.0;
  static const double categoryItemHeight = 80.0;

  // أحجام الخريطة
  static const double mapHeight = 200.0;
  static const double mapHeightFull = 300.0;
  static const double mapMarkerSize = 40.0;

  // أحجام القوائم
  static const double listItemHeight = 56.0;
  static const double listItemHeightSmall = 48.0;
  static const double listItemHeightLarge = 72.0;

  // أحجام الشبكة
  static const double gridSpacing = 8.0;
  static const double gridCrossAxisSpacing = 8.0;
  static const double gridMainAxisSpacing = 8.0;
  static const double gridChildAspectRatio = 0.8;

  // أحجام الحاويات
  static const double containerMinHeight = 48.0;
  static const double containerMaxWidth = 600.0;
  
  // نقاط التوقف للاستجابة (Responsive Breakpoints)
  static const double mobileBreakpoint = 600.0;
  static const double tabletBreakpoint = 900.0;
  static const double desktopBreakpoint = 1200.0;
}
