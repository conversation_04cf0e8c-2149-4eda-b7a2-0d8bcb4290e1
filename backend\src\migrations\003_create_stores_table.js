/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function(knex) {
  return knex.schema.createTable('stores', function(table) {
    table.string('id', 36).primary().defaultTo(knex.raw("(lower(hex(randomblob(4))) || '-' || lower(hex(randomblob(2))) || '-4' || substr(lower(hex(randomblob(2))),2) || '-' || substr('89ab',abs(random()) % 4 + 1, 1) || substr(lower(hex(randomblob(2))),2) || '-' || lower(hex(randomblob(6))))"));
    table.string('owner_id', 36).notNullable().references('id').inTable('users').onDelete('CASCADE');
    table.string('name', 100).notNullable();
    table.text('description').nullable();
    table.string('logo').nullable();
    table.string('cover_image').nullable();
    table.enum('category', [
      'restaurant', 'cafe', 'grocery', 'pharmacy', 
      'electronics', 'clothing', 'books', 'other'
    ]).notNullable();
    table.string('phone', 15).nullable();
    table.string('email').nullable();
    table.text('address').notNullable();
    table.text('location').notNullable(); // Store as JSON string for SQLite
    table.decimal('delivery_fee', 8, 2).defaultTo(0);
    table.decimal('min_order', 8, 2).defaultTo(0);
    table.decimal('max_delivery_distance', 5, 2).defaultTo(10); // in km
    table.time('opening_time').nullable();
    table.time('closing_time').nullable();
    table.json('working_days').nullable(); // Array of days [0-6] where 0 is Sunday
    table.boolean('is_active').defaultTo(true);
    table.boolean('is_approved').defaultTo(false);
    table.decimal('rating', 3, 2).defaultTo(0);
    table.integer('total_orders').defaultTo(0);
    table.integer('total_reviews').defaultTo(0);
    table.json('features').nullable(); // Array of features like ['delivery', 'pickup', 'cash', 'card']
    table.timestamp('approved_at').nullable();
    table.string('approved_by', 36).nullable().references('id').inTable('users');
    table.timestamps(true, true);
    
    // Indexes
    table.index(['owner_id']);
    table.index(['category']);
    table.index(['is_active']);
    table.index(['is_approved']);
    table.index(['rating']);
    table.index(['name']);
  });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function(knex) {
  return knex.schema.dropTable('stores');
};
