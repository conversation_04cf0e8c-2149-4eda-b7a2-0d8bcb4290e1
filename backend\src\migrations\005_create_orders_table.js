/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function(knex) {
  return knex.schema.createTable('orders', function(table) {
    table.string('id', 36).primary().defaultTo(knex.raw("(lower(hex(randomblob(4))) || '-' || lower(hex(randomblob(2))) || '-4' || substr(lower(hex(randomblob(2))),2) || '-' || substr('89ab',abs(random()) % 4 + 1, 1) || substr(lower(hex(randomblob(2))),2) || '-' || lower(hex(randomblob(6))))"));
    table.string('order_number', 20).notNullable().unique();
    table.string('user_id', 36).notNullable().references('id').inTable('users').onDelete('CASCADE');
    table.string('store_id', 36).notNullable().references('id').inTable('stores').onDelete('CASCADE');
    table.string('captain_id', 36).nullable().references('id').inTable('captains').onDelete('SET NULL');
    table.enum('status', [
      'pending', 'confirmed', 'preparing', 'ready', 
      'picked_up', 'on_the_way', 'delivered', 'cancelled'
    ]).defaultTo('pending');
    table.decimal('subtotal', 10, 2).notNullable();
    table.decimal('delivery_fee', 8, 2).defaultTo(0);
    table.decimal('service_fee', 8, 2).defaultTo(0);
    table.decimal('discount_amount', 8, 2).defaultTo(0);
    table.decimal('total_amount', 10, 2).notNullable();
    table.enum('payment_method', [
      'cash', 'vodafone_cash', 'fawry', 'orange_cash', 'etisalat_cash'
    ]).notNullable();
    table.enum('payment_status', ['pending', 'paid', 'failed', 'refunded']).defaultTo('pending');
    table.string('payment_reference').nullable();
    table.text('delivery_address').notNullable();
    table.text('delivery_location').notNullable(); // Store as JSON string for SQLite
    table.string('delivery_phone', 15).nullable();
    table.text('delivery_notes').nullable();
    table.timestamp('estimated_delivery_time').nullable();
    table.timestamp('confirmed_at').nullable();
    table.timestamp('prepared_at').nullable();
    table.timestamp('picked_up_at').nullable();
    table.timestamp('delivered_at').nullable();
    table.timestamp('cancelled_at').nullable();
    table.text('cancellation_reason').nullable();
    table.string('cancelled_by', 36).nullable().references('id').inTable('users');
    table.timestamps(true, true);
    
    // Indexes
    table.index(['order_number']);
    table.index(['user_id']);
    table.index(['store_id']);
    table.index(['captain_id']);
    table.index(['status']);
    table.index(['payment_status']);
    table.index(['created_at']);
  });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function(knex) {
  return knex.schema.dropTable('orders');
};
