import 'dart:developer' as developer;
import 'package:flutter/foundation.dart';

/// نظام تسجيل الأحداث للتطبيق
class AppLogger {
  AppLogger._();

  static const String _tag = 'TragoApp';

  /// تسجيل معلومات عامة
  static void info(String message, {String? tag}) {
    if (kDebugMode) {
      developer.log(
        message,
        name: tag ?? _tag,
        level: 800, // INFO level
      );
    }
  }

  /// تسجيل تحذيرات
  static void warning(String message, {String? tag}) {
    if (kDebugMode) {
      developer.log(
        message,
        name: tag ?? _tag,
        level: 900, // WARNING level
      );
    }
  }

  /// تسجيل أخطاء
  static void error(String message, {Object? error, StackTrace? stackTrace, String? tag}) {
    if (kDebugMode) {
      developer.log(
        message,
        name: tag ?? _tag,
        level: 1000, // ERROR level
        error: error,
        stackTrace: stackTrace,
      );
    }
  }

  /// تسجيل أحداث التطبيق
  static void debug(String message, {String? tag}) {
    if (kDebugMode) {
      developer.log(
        message,
        name: tag ?? _tag,
        level: 700, // DEBUG level
      );
    }
  }

  /// تسجيل أحداث الشبكة
  static void network(String message, {String? tag}) {
    if (kDebugMode) {
      developer.log(
        message,
        name: '${tag ?? _tag}_NETWORK',
        level: 800,
      );
    }
  }

  /// تسجيل أحداث الإشعارات
  static void notification(String message, {String? tag}) {
    if (kDebugMode) {
      developer.log(
        message,
        name: '${tag ?? _tag}_NOTIFICATION',
        level: 800,
      );
    }
  }

  /// تسجيل أحداث التخزين
  static void storage(String message, {String? tag}) {
    if (kDebugMode) {
      developer.log(
        message,
        name: '${tag ?? _tag}_STORAGE',
        level: 800,
      );
    }
  }

  /// تسجيل أحداث التوجيه
  static void navigation(String message, {String? tag}) {
    if (kDebugMode) {
      developer.log(
        message,
        name: '${tag ?? _tag}_NAVIGATION',
        level: 800,
      );
    }
  }

  /// تسجيل أحداث المصادقة
  static void auth(String message, {String? tag}) {
    if (kDebugMode) {
      developer.log(
        message,
        name: '${tag ?? _tag}_AUTH',
        level: 800,
      );
    }
  }

  /// تسجيل معلومات التكوين
  static void config(String message, {String? tag}) {
    if (kDebugMode) {
      developer.log(
        message,
        name: '${tag ?? _tag}_CONFIG',
        level: 800,
      );
    }
  }
}
