/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function(knex) {
  return knex.schema.createTable('addresses', function(table) {
    table.string('id', 36).primary().defaultTo(knex.raw("(lower(hex(randomblob(4))) || '-' || lower(hex(randomblob(2))) || '-4' || substr(lower(hex(randomblob(2))),2) || '-' || substr('89ab',abs(random()) % 4 + 1, 1) || substr(lower(hex(randomblob(2))),2) || '-' || lower(hex(randomblob(6))))"));
    table.string('user_id', 36).notNullable().references('id').inTable('users').onDelete('CASCADE');
    table.string('title', 50).notNullable(); // e.g., 'Home', 'Work', 'Other'
    table.text('address').notNullable();
    table.string('building_number', 20).nullable();
    table.string('floor_number', 10).nullable();
    table.string('apartment_number', 10).nullable();
    table.string('landmark').nullable();
    table.text('location').notNullable(); // Store as JSON string for SQLite
    table.string('phone', 15).nullable();
    table.boolean('is_default').defaultTo(false);
    table.timestamps(true, true);
    
    // Indexes
    table.index(['user_id']);
    table.index(['is_default']);
  });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function(knex) {
  return knex.schema.dropTable('addresses');
};
