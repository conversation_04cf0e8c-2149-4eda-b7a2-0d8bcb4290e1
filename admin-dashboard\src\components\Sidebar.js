import React from 'react';
import { Layout, Menu } from 'antd';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  DashboardOutlined,
  UserOutlined,
  ShopOutlined,
  CarOutlined,
  ShoppingOutlined,
} from '@ant-design/icons';

const { Sider } = Layout;

const Sidebar = ({ collapsed }) => {
  const navigate = useNavigate();
  const location = useLocation();

  const menuItems = [
    {
      key: '/dashboard',
      icon: <DashboardOutlined />,
      label: 'لوحة التحكم',
    },
    {
      key: '/users',
      icon: <UserOutlined />,
      label: 'المستخدمين',
    },
    {
      key: '/stores',
      icon: <ShopOutlined />,
      label: 'المتاجر',
    },
    {
      key: '/captains',
      icon: <CarOutlined />,
      label: 'الكباتن',
    },
    {
      key: '/orders',
      icon: <ShoppingOutlined />,
      label: 'الطلبات',
    },
  ];

  const handleMenuClick = ({ key }) => {
    navigate(key);
  };

  return (
    <Sider
      trigger={null}
      collapsible
      collapsed={collapsed}
      style={{
        overflow: 'auto',
        height: '100vh',
        position: 'fixed',
        right: 0,
        top: 0,
        bottom: 0,
        zIndex: 100,
      }}
    >
      <div
        style={{
          height: 64,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          background: 'rgba(255, 255, 255, 0.1)',
          margin: 16,
          borderRadius: 8,
        }}
      >
        <h2 style={{ color: 'white', margin: 0 }}>
          {collapsed ? 'T' : 'تراجو'}
        </h2>
      </div>
      
      <Menu
        theme="dark"
        mode="inline"
        selectedKeys={[location.pathname]}
        items={menuItems}
        onClick={handleMenuClick}
        style={{ borderRight: 0 }}
      />
    </Sider>
  );
};

export default Sidebar;
